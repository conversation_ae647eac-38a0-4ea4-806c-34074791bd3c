/**
 * 指定されたIDの<select>要素にオプションを生成して追加する。
 * @param {string} selectId - <select>要素のID。
 * @param {Array<string|object>} options - オプションの配列。オブジェクトの場合は{value, text}形式。
 */
export function populateSelect (selectId, options, keyMap = {}) { // keyMapのデフォルト値を設定
  // select要素のIDに基づいて、使用するキーマップを切り替える
  const selectKeyMap = selectId === 'rounds' ? {} : keyMap

  const selectElement = document.getElementById(selectId)
  if (!selectElement) {
    console.warn(`Element with ID '${selectId}' not found.`)
    return
  }
  // ★修正: innerHTML = '' はイベントリスナーを削除してしまうため、
  // 子要素を一つずつ削除する安全な方法に変更する。
  while (selectElement.firstChild) {
    selectElement.removeChild(selectElement.lastChild)
  }

  options.forEach((optionConfig) => {
    const opt = document.createElement('option')

    if (typeof optionConfig === 'object' && optionConfig !== null && 'value' in optionConfig && 'text' in optionConfig) {
      opt.value = optionConfig.value
      opt.textContent = optionConfig.text
      const key = selectKeyMap ? Object.keys(selectKeyMap).find(key => selectKeyMap[key] === optionConfig.value) : undefined
      if (key) opt.textContent += ` [${key}]`
      if (optionConfig.value === '') {
        opt.disabled = true
        opt.selected = true
      }
    } else { // MACHINES, STAGESなど、文字列の配列を受け取る場合
      opt.value = optionConfig
      const key = selectKeyMap ? Object.keys(selectKeyMap).find(key => selectKeyMap[key] === optionConfig) : undefined
      opt.textContent = optionConfig + (key ? ` [${key}]` : '')
    }
    selectElement.appendChild(opt)
  })
}

/**
 * '勝利'/'敗北'などの日本語表記を'WIN'/'LOSE'などの英語表記に正規化する。
 * @param {string} resultString - 正規化する勝敗文字列。
 * @returns {string} 正規化された勝敗文字列 ('WIN', 'LOSE', 'DRAW', 'UNKNOWN')。
 */
export function normalizeResult (resultString) {
  if (!resultString) return 'UNKNOWN'
  const upperCaseResult = resultString.toUpperCase()

  if (upperCaseResult === 'WIN' || upperCaseResult === '勝利') {
    return 'WIN'
  } else if (upperCaseResult === 'LOSE' || upperCaseResult === '敗北') {
    return 'LOSE'
  } else if (upperCaseResult === 'DRAW' || upperCaseResult === '引き分け') {
    return 'DRAW'
  }
  return 'UNKNOWN'
}

/**
 * ASSから受信したJSONデータのキーや値を、DBに保存する形式に変換（マッピング）する。
 * @param {object} assData - ASSから出力された生のJSONデータ。
 * @returns {object} DB保存形式に変換された対戦データオブジェクト。
 */
export function mapAssDataToMatchData (assData) {
  let resultMapping = 'UNKNOWN'
  if (assData.result === '勝利') {
    resultMapping = 'WIN'
  } else if (assData.result === '敗北') {
    resultMapping = 'LOSE'
  } else if (assData.result === '引き分け' || assData.result === 'DRAW') {
    resultMapping = 'DRAW'
  }

  let roundsString = null
  if (resultMapping === 'DRAW') {
    roundsString = 'DRAW'
  } else if (typeof assData.rounds_won === 'number' && typeof assData.rounds_lost === 'number') {
    roundsString = `${assData.rounds_won} vs ${assData.rounds_lost}`
  }

  return {
    id: Date.now(),
    myMachine: assData.my_character || '不明',
    opponentMachine: assData.opponent_character || '不明',
    stage: assData.stage || '不明',
    result: resultMapping,
    rounds: roundsString,
    // ★修正: 対戦相手名をassData.opponent_nameから取得
    // ASSデータにopponent_nameが存在しない場合も考慮し、nullish coalescing operator (??) を使用
    opponentName: assData.opponent_name ?? null
  }
}

/**
 * 分析テーブルのデータ行（<tr>）を生成する。
 * @param {Array<string|number>} cells - 行に表示するセルの内容の配列。
 * @param {string} [cellClass=''] - セルに適用するCSSクラス（例: 'result-win'）。
 * @returns {HTMLTableRowElement} 生成された<tr>要素。
 */
export function createAnalysisTableRow (cells, cellClass = '') {
  const row = document.createElement('tr')
  cells.forEach((cellContent, index) => {
    const cell = document.createElement('td')
    cell.textContent = cellContent
    if (cellClass && index === 0) { // 最初のセルにのみクラスを適用する例
      cell.classList.add(cellClass)
    }
    row.appendChild(cell)
  })
  return row
}

/**
 * 指定されたヘッダーとデータからテーブル要素を生成する。
 * @param {Array<string>} headers - テーブルのヘッダーセルの内容の配列。
 * @param {Array<Array<string|number>>} data - テーブルのデータ行の配列。各行はセルの内容の配列。
 * @param {string} [tableClass=''] - テーブルに適用するCSSクラス。
 * @returns {HTMLTableElement} 生成された<table>要素。
 */
export function createTable (headers, data, tableClass = '') {
  const table = document.createElement('table')
  if (tableClass) {
    table.classList.add(tableClass)
  }

  // ヘッダーの生成
  const thead = document.createElement('thead')
  const headerRow = document.createElement('tr')
  headers.forEach(headerText => {
    const headerCell = document.createElement('th')
    headerCell.textContent = headerText
    headerRow.appendChild(headerCell)
  })
  thead.appendChild(headerRow)
  table.appendChild(thead)

  // データ行の生成 (createAnalysisTableRow を使用)
  const tbody = document.createElement('tbody')
  data.forEach(rowData => {
    tbody.appendChild(createAnalysisTableRow(rowData))
  })
  table.appendChild(tbody)

  return table
}
