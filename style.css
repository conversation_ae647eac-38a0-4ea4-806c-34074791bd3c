body {
    font-family: sans-serif;
    margin: 20px;
    background-color: #f0f0f0;
}
h1, h2 {
    color: #333;
}
div {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
}

/* ★★★ここからsetting-sectionのスタイルを追加★★★ */
.settings-section {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* 影を追加 */
}
.settings-section label {
    font-size: 1.1em;
    font-weight: bold;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 600px;
}
.settings-section input[type="checkbox"] {
    margin-right: 10px;
    width: 20px;
    height: 20px;
}
/* ★★★ここまでsetting-sectionのスタイルを追加★★★ */

/* ★★★メッセージ表示用のスタイル ★★★ */
#status, #message {
    font-size: 0.9em;
    margin-top: 5px;
    text-align: center;
    font-weight: bold;
}
#status { color: gray; }
#message { color: #007bff; }
/* ★★★メッセージ表示用のスタイルここまで★★★ */

label {
    display: inline-block;
    width: 150px;
    margin-bottom: 5px;
    font-weight: bold;
}
input[type="text"], select {
    width: 200px;
    padding: 5px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
}
button {
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}
button:hover {
    background-color: #0056b3;
}
hr {
    margin: 30px 0;
    border-top: 1px solid #eee;
}

#record-list-section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    
    /* ★ここを追加・修正！ */
    max-height: 350px; /* 例えば10件表示されるくらいの高さに設定（調整してみてね！） */
    overflow-y: auto;  /* 縦方向に内容が溢れたらスクロールバーを表示 */
}

#matches-table {
    width: 100%; /* 親要素の幅いっぱいに広げる */
    border-collapse: collapse; /* セルのボーダーを結合 */
}

#matches-table th,
#matches-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    white-space: nowrap; /* テキストの折り返しを防ぐ */
}

#matches-table th {
    background-color: #f2f2f2;
    position: sticky; /* スクロールしてもヘッダーが固定されるように */
    top: 0;          /* 上部に固定 */
    z-index: 1;      /* 他のコンテンツの上に表示 */
}

/* ヘッダーの背景色でスクロールバーが見えなくなる場合があるので調整 */
#record-list-section::-webkit-scrollbar {
    width: 8px;
}
#record-list-section::-webkit-scrollbar-track {
    background: #f1f1f1;
}
#record-list-section::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}
#record-list-section::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 記録がない場合のメッセージのスタイル（必要なら） */
#no-records-message {
    padding: 10px;
    text-align: center;
    color: #666;
}

/* 新しく追加する分析テーブルのスタイル */
.analysis-table {
    width: 100%; /* 親要素の幅いっぱいに広げる */
    border-collapse: collapse; /* セルのボーダーを結合 */
    margin-bottom: 20px; /* 他のセクションとの間に余白 */
}

.analysis-table th,
.analysis-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.analysis-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

/* 数字の列は右寄せにした方が見やすいかも（任意） */
.analysis-table td:nth-child(2), /* 勝数 */
.analysis-table td:nth-child(3), /* 敗数 */
.analysis-table td:nth-child(4), /* 勝率 */
.analysis-table td:nth-child(5)  /* 総試合数 */
{
    text-align: right;
}

/* 全体の勝敗率の表示も少し調整 */
#overall-winrate {
    font-size: 1.1em;
    font-weight: bold;
    text-align: center;
    padding: 10px;
    margin-bottom: 20px; /* テーブルとの間に余白 */
}

/* ★★★ここを追加！スクロール可能なテーブルコンテナのスタイル★★★ */
.scrollable-table-container {
    max-height: 300px; /* テーブルの最大高さを設定。必要に応じて調整してね！ */
    overflow-y: auto;  /* 縦方向に内容がはみ出したらスクロールバーを表示する */
    border: 1px solid #ccc; /* 任意：スクロールエリアを分かりやすくするための枠線 */
    padding: 5px; /* 任意：内側の余白 */
    margin-top: 10px; /* 任意：上部の余白 */
}

/* テーブルのヘッダーがスクロールしても固定されるようにする場合（高度な設定） */
/* これは少し複雑になるので、まずは上記の基本スタイルで試してみて、必要なら検討しよう！ */
/* .scrollable-table-container table {
    width: 100%;
    border-collapse: collapse;
}

.scrollable-table-container thead th {
    position: sticky;
    top: 0;
    background-color: #f2f2f2;
    z-index: 10;
} */

/* ★★★ここを追加！危険な操作のボタンを赤色にするよ★★★ */
.button-danger {
    background-color: #e74c3c; /* 鮮やかな赤色 */
    color: white; /* 文字色は白で見やすく */
    border: 1px solid #c0392b; /* 少し濃い目の赤で境界線 */
}

/* ホバー時のスタイル（任意：マウスを乗せたときに少し色を変える） */
.button-danger:hover {
    background-color: #c0392b; /* ホバーで少し暗めの赤に */
    cursor: pointer; /* マウスカーソルをポインターにする */
}
/* ★★★ここまで追加！★★★ */

/* ★★★ここを修正！文字色を黒に、背景色を薄めの青にするよ★★★ */
/* 分析セクションの見出し h3 のスタイル */
#analysis-section h3 {
    background-color: #A9D1EF; /* 提案1: ソフトな水色 */
    /* background-color: #B4DBF2; */ /* 提案2: 少し明るめの水色 */
    /* background-color: #C9E6F6; */ /* 提案3: より淡い水色 */
    color: #333333; /* 文字色を濃いグレー（ほぼ黒）に */
    padding: 10px 15px; 
    margin-top: 20px; 
    margin-bottom: 10px; 
    display: block; 
    box-sizing: border-box; 
}

/* 各分析項目の見出し h4 のスタイル */
#analysis-section h4 {
    background-color: #BEE3F8; /* 提案1: h3 より少し薄い水色 */
    /* background-color: #C7EAFB; */ /* 提案2: h3 より少し明るい水色 */
    /* background-color: #DDEEF9; */ /* 提案3: h3 よりさらに淡い水色 */
    color: #333333; /* 文字色を濃いグレー（ほぼ黒）に */
    padding: 8px 12px; 
    margin-top: 15px; 
    margin-bottom: 8px; 
    display: block; 
    box-sizing: border-box; 
}
/* ★★★修正ここまで！★★★ */

/* 勝利時の色 */
.result-win {
    color: #4CAF50; /* 緑色 */
    font-weight: bold;
}

/* 敗北時の色 */
.result-lose {
    color: #F44336; /* 赤色 */
    font-weight: bold;
}

/* 引き分け（DRAW）やその他（未入力など）の色 */
.result-draw,
.result-other {
    color: #2196F3; /* 青色 */
    /* font-weight: bold; */ /* 必要なら bold に */
}

/*ここから直近25戦の結果のスタイル*/
.app-container {
    background-color: #f0f0f0;
    color: #333;
}

.recent-matches-summary {
    background-color: #ffffff;
    padding: 20px;
    margin-bottom: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    color: #333;
    text-align: center;
    border: 1px solid #ddd;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-style: none; /*ボーダー無し */
}

.recent-matches-summary h2 {
    color: #333;
    margin: 0;
    font-size: 1.6em;
    text-shadow: none;
    letter-spacing: 0;
}

.reset-button {
    background-color: #e0e0e0;
    color: #555;
    border: 1px solid #ccc;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease, border-color 0.3s ease;
    box-shadow: none;
}

.reset-button:hover {
    background-color: #d0d0d0;
    border-color: #bbb;
}

.recent-count-display {
    font-size: 2.2em;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
    letter-spacing: 0;
    text-shadow: none;
}

.recent-marks-area {
    overflow-x: auto; /* ★ここが重要！これで横スクロールするよ！★ */
    padding-bottom: 30px;
    margin-top: 15px;
    position: relative;
    padding-top: 5px;
    width: 100%; /* 親要素の幅いっぱいを使う */
    box-sizing: border-box; /* paddingを含めた幅計算 */
    border-style: none; /*ボーダー無し */
}

.recent-marks-container {
    display: flex;
    flex-wrap: nowrap; /* 折り返さない */
    justify-content: flex-start; /* ★ここが左揃えの命令！★ */
    padding-bottom: 5px;
    position: relative;
    z-index: 2;
    /* width: calc(22px * 25); /* ここは不要にする！flexアイテムの幅に任せる */
    border-style: none; /*ボーダー無し */
}

.recent-mark {
    /* ★ここを20pxに修正！★ */
    min-width: 20px; /* マークの幅 */
    width: 20px; /* 明示的に幅を設定 */
    height: 20px; /* 高さも合わせる */
    font-size: 1.0em; /* マークの文字サイズ */
    margin: 0 1px; /* マーク間のスペースを調整 */
    border-radius: 3px;
    box-shadow: none;
    font-family: 'Arial', sans-serif;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    /* ★box-sizing: border-box; を追加して、borderやpaddingを含めて20pxにする★ */
    box-sizing: border-box; 
}

.recent-mark.win {
    background-color: #28a745;
    color: #fff;
}

.recent-mark.lose {
    background-color: #dc3545;
    color: #fff;
}

.recent-mark.draw {
    background-color: #007bff;
    color: #fff;
}

.recent-mark.none {
    background-color: #eee;
    color: #aaa;
    border: 1px dashed #ccc;
    /* ★noneの場合もbox-sizingを適用して幅を統一する★ */
    box-sizing: border-box;
}

.recent-marks-guide {
    position: absolute;
    bottom: -1px;
    color: #555;
    padding-bottom: 30px;
    border-style: none; /*ボーダー無し */
}

.recent-marks-guide span {
    position: absolute; /* ★変更なし: 各数字を絶対配置★ */
    transform: translateX(-50%); /* 数字の中央が指定位置に来るように */
    font-weight: bold;
    color: #444;
}

/* スクロール可能なテーブルコンテナの共通スタイル */
.scrollable-table-container {
    max-height: 300px; /* 必要に応じて高さを調整 */
    overflow-y: auto;
    position: relative;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 15px;
    background-color: #fff;
}

/* テーブルヘッダー固定の共通スタイル */
.scrollable-table-container table thead th,
#matches-table thead th { /* 既存の対戦記録一覧のヘッダーにも適用されるようにしておく */
    position: sticky;
    top: 0;
    background-color: #f8f8f8; /* 背景色を設定しないと、スクロール時に文字が透けて見えちゃうよ */
    z-index: 10; /* 他のコンテンツの上に表示 */
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1); /* 影で立体感 */
    padding: 12px 15px;
    text-align: left;
    white-space: nowrap;
}

/* テーブル全体の共通スタイル */
.scrollable-table-container table,
#matches-table {
    width: 100%;
    border-collapse: collapse;
}

.scrollable-table-container table th,
.scrollable-table-container table td,
#matches-table th,
#matches-table td {
    padding: 10px 15px;
    border: 1px solid #e9e9e9;
}

.scrollable-table-container table tbody tr:nth-child(odd),
#matches-table tbody tr:nth-child(odd) {
    background-color: #fefefe;
}

.scrollable-table-container table tbody tr:hover,
#matches-table tbody tr:hover {
    background-color: #f0f0f0;
}

/* ★★★ここからセレクトボックスのプレースホルダー表示スタイルを追加★★★ */
/* セレクトボックスがプレースホルダー（値が空）のときに文字色を薄い灰色にする */
select.is-placeholder {
    color: #999; /* 薄い灰色 */
}

/* セレクトボックスに値が選択されている場合は通常の黒色 */
select:not(.is-placeholder) {
    color: #333; /* 通常のテキスト色 */
}

/* ドロップダウン内のプレースホルダーオプション自体の色 */
select option[value=""][disabled] {
    color: #999; /* 薄い灰色 */
}
/* ★★★ここまでセレクトボックスのプレースホルダー表示スタイルを追加★★★ */