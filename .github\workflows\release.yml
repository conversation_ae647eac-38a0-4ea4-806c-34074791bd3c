# This workflow handles creating a new GitHub Release and uploading build artifacts.
name: Release

# This workflow is triggered when a new tag starting with 'v' is pushed.
on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    # We'll use a Windows runner to build the Windows installer.
    runs-on: windows-latest

    # ★★★ここを追加！★★★
    # GitHub Releasesを作成するために、コンテンツへの書き込み権限を付与する
    permissions:
      contents: write

    steps:
      # 1. Check out the repository's code.
      - name: 1. Checkout code
        uses: actions/checkout@v4

      # 2. Set up the Node.js environment.
      - name: 2. Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      # 3. Install project dependencies.
      - name: 3. Install dependencies
        run: npm install

      # 4. Build the application using electron-builder.
      - name: 4. Build application
        run: npm run build

      # 5. Create a GitHub Release and upload the built assets.
      - name: 5. Create Release and Upload Assets
        uses: softprops/action-gh-release@v2
        with:
          # The GITHUB_TOKEN is automatically provided by GitHub Actions.
          # 'files' specifies which build artifacts to upload.
          # We use wildcards to catch the setup file regardless of the version number.
          files: |
            dist/*.exe
            dist/*.zip
          # This will automatically generate release notes from your commit history.
          generate_release_notes: true