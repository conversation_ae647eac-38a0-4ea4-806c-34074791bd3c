// main.js

const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fsp = require('node:fs/promises'); // 非同期ファイル操作用
const fs = require('fs'); // 既存のfsモジュール (CSVエクスポートなどでまだ使うため)
const chokidar = require('chokidar'); // chokidarをインポート

// lowdbのセットアップ
const defaultData = { matches: [] };
const dataPath = path.join(app.getPath('userData'), 'db.json');
const { Low } = require('lowdb');
const { JSONFileSync } = require('lowdb/node');
const db = new Low(new JSONFileSync(dataPath), defaultData);

db.read(); // アプリ起動時に一度読み込む

let mainWindow;

// ★★★ 監視対象フォルダのパス ★★★
const WATCH_DIR = path.join(app.getPath('userData'), 'match_data_in');

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 800,
        height: 850,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            contextIsolation: true,
            nodeIntegration: false,
            enableRemoteModule: false
        },
        title: '電脳戦機バーチャロン"OMG" 対戦記録アプリ',
    });

    mainWindow.loadFile('index.html');

    // デバッグ用にDevToolsを開く
    // mainWindow.webContents.openDevTools();
}

app.whenReady().then(() => {
    createWindow();

    app.on('activate', function () {
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });

    // ★★★ アプリ起動後にファイル監視を開始 ★★★
    startFileWatcher();
});

app.on('window-all-closed', function () {
    if (process.platform !== 'darwin') app.quit();
});


// --- ファイル監視ロジック ---
async function startFileWatcher() {
    // 監視ディレクトリが存在しない場合は作成
    try {
        await fsp.mkdir(WATCH_DIR, { recursive: true });
        console.log(`[Watcher] 監視ディレクトリ: ${WATCH_DIR} を作成しました (または既に存在します)。`);
    } catch (error) {
        console.error(`[Watcher] 監視ディレクトリの作成に失敗しました: ${error}`);
        if (mainWindow) {
            mainWindow.webContents.send('watcher-error', `監視ディレクトリの作成に失敗しました: ${error.message}`);
        }
        return; // 作成失敗時は監視を開始しない
    }

    // chokidar ウォッチャーの作成
    const watcher = chokidar.watch(WATCH_DIR, {
        persistent: true, // アプリ終了まで監視を続ける
        ignoreInitial: true, // 初期起動時に既存ファイルをイベントとして扱わない
        depth: 0, // 監視対象の深さ（0は直下のファイルのみ）
        awaitWriteFinish: { // ファイル書き込み完了を待つ設定
            stabilityThreshold: 1000, // ファイルが1秒間変更されなければ書き込み完了とみなす
            pollInterval: 100 // 変更チェックの間隔
        }
    });

    console.log(`[Watcher] ファイル監視を開始しました: ${WATCH_DIR}`);

    // 新しいファイルが追加された時のイベント
    watcher.on('add', async (filePath) => {
        console.log(`[Watcher] 新しいファイルが検知されました: ${filePath}`);
        // JSONファイルのみを処理
        if (path.extname(filePath).toLowerCase() === '.json') {
            try {
                // ファイルを読み込む (fsp.readFileを使用)
                const data = await fsp.readFile(filePath, 'utf8');
                const jsonData = JSON.parse(data);

                console.log('[Watcher] JSONデータを読み込みました:', jsonData);

                // レンダラープロセスにデータを送信
                if (mainWindow) {
                    mainWindow.webContents.send('new-match-data', jsonData);
                    console.log('[Watcher] データをレンダラープロセスに送信しました。');
                }

                // 読み込み後にファイルを削除 (fsp.unlinkを使用)
                await fsp.unlink(filePath);
                console.log(`[Watcher] ファイルを削除しました: ${filePath}`);

            } catch (error) {
                console.error(`[Watcher] ファイルの読み込みまたは処理中にエラーが発生しました (${filePath}):`, error);
                if (mainWindow) {
                    mainWindow.webContents.send('file-processing-error', `ファイルの処理中にエラーが発生しました: ${path.basename(filePath)}`);
                }
            }
        } else {
            console.log(`[Watcher] 非JSONファイル (${path.extname(filePath)}) はスキップしました: ${filePath}`);
        }
    });

    // エラーハンドリング
    watcher.on('error', (error) => {
        console.error(`[Watcher] ウォッチャーエラー: ${error}`);
        if (mainWindow) {
            mainWindow.webContents.send('watcher-error', `ファイル監視中にエラーが発生しました: ${error.message}`);
        }
    });
}


// --- ここからIPCハンドラー ---

// get-matches ハンドラー
ipcMain.handle('get-matches', async () => {
    try {
        await db.read();
        const matches = (db.data && Array.isArray(db.data.matches)) ? db.data.matches : [];
        const sortedMatches = matches.sort((a, b) => b.id - a.id);
        return sortedMatches.slice(0, 100);

    } catch (error) {
        console.error('記録の取得エラー:', error);
        return [];
    }
});

// add-match ハンドラー
ipcMain.handle('add-match', async (event, matchData) => {
    try {
        await db.read();
        db.data = db.data || defaultData;
        db.data.matches = db.data.matches || [];
        
        db.data.matches.push(matchData);

        await db.write();
        
        return db.data.matches;
    } catch (error) {
        console.error('[add-match] 試合記録の追加エラー:', error);
        throw new Error(`試合記録の追加に失敗しました: ${error.message}`);
    }
});

// CSVエクスポート
ipcMain.handle('export-csv', async () => {
    await db.read();
    const records = db.data.matches || [];

    if (records.length === 0) {
        dialog.showMessageBoxSync({
            type: 'info',
            title: 'エクスポート',
            message: 'エクスポートするデータがないよ。',
            buttons: ['OK']
        });
        return null;
    }

    const bom = '\ufeff';
    let csvContent = '';
    csvContent += '"日時","使用機体","相手機体","ステージ","勝敗","ラウンド","相手名","ID"\n';

    records.forEach(record => {
        const formattedDate = new Date(record.id).toLocaleString('ja-JP', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        csvContent += `"${formattedDate}","${record.myMachine}","${record.opponentMachine}","${record.stage}","${record.result}","${record.rounds || ''}","${record.opponentName || ''}","${record.id}"\n`;
    });

    return new Promise((resolve) => {
        dialog.showSaveDialog(mainWindow, { // getFocusedWindow() から mainWindow に変更
            title: 'CSVファイルを保存',
            defaultPath: `対戦記録_${new Date().toLocaleDateString('ja-JP').replace(/\//g, '-')}.csv`,
            filters: [{ name: 'CSV Files', extensions: ['csv'] }]
        }).then(result => {
            if (!result.canceled && result.filePath) {
                // fs.writeFileはcallbackベースなのでそのまま
                fs.writeFile(result.filePath, bom + csvContent, (writeErr) => {
                    if (writeErr) {
                        console.error('CSV write error:', writeErr);
                        resolve(null);
                    } else {
                        resolve(result.filePath);
                    }
                });
            } else {
                resolve(null);
            }
        }).catch(dialogErr => {
            console.error('Show save dialog error:', dialogErr);
            resolve(null);
        });
    });
});

// 削除関連のIPCハンドラー
ipcMain.handle('confirmFirstDelete', async () => {
    const response = await dialog.showMessageBox(mainWindow, { // getFocusedWindow() から mainWindow に変更
        type: 'question',
        title: '対戦記録の全削除',
        message: '本当にすべての対戦記録を削除しますか？',
        buttons: ['はい', 'いいえ'],
        defaultId: 1,
        cancelId: 1
    });
    return response.response === 0;
});

ipcMain.handle('confirmFinalDelete', async () => {
    const response = await dialog.showMessageBox(mainWindow, { // getFocusedWindow() から mainWindow に変更
        type: 'warning',
        title: '最終確認！重大な操作です',
        message: 'この操作を実行すると、すべての対戦記録が完全に削除され、元に戻すことはできません。\n\n本当に続行しますか？',
        buttons: ['はい、削除します', 'キャンセル'],
        defaultId: 1,
        cancelId: 1
    });
    return response.response === 0;
});

ipcMain.handle('deleteAllRecords', async () => {
    try {
        await db.read();
        db.data.matches = []; // データ配列を空にする
        await db.write();
        return true;
    } catch (error) {
        console.error('Failed to delete all records:', error);
        return false;
    }
});

// forceReactivateWindow ハンドラー
ipcMain.handle('forceReactivateWindow', () => {
    if (mainWindow) {
        mainWindow.blur();
        setTimeout(() => {
            mainWindow.focus();
        }, 50);
    }
});
