// main.js

const { app, BrowserWindow, ipcMain, dialog } = require('electron')
const path = require('node:path')
const { Low } = require('lowdb') // Low は lowdb からインポート
const { JSONFile } = require('lowdb/node')
const fsp = require('node:fs/promises')
const iconv = require('iconv-lite') // ★追加: 文字コード変換ライブラリ
let mainWindow // メインウィンドウの参照を保持

// データベースの初期化
const adapter = new JSONFile(path.join(app.getPath('userData'), 'db.json'))
const db = new Low(adapter, { matches: [] }) // ★修正: lowdb v7ではコンストラクタでデフォルト値を渡す

/**
 * データベースファイルを読み込み、存在しない場合は初期化する。
 * 致命的なエラーが発生した場合は、ダイアログを表示してアプリを終了する。
 */
async function initializeDb () {
  try {
    await db.read() // db.jsonを読み込む。ファイルがなければデフォルト値が使われる
    await db.write()
  } catch (error) {
    console.error('データベースの初期化中にエラーが発生しました:', error)
    // 致命的なエラーなのでダイアログを表示してアプリを終了する
    dialog.showErrorBox('ごめんね！データベースエラー', `データベースの読み込みに失敗しちゃった。アプリを終了するね。\n\nエラー詳細: ${error.message}`)
    app.quit()
  }
}

/**
 * メインウィンドウを作成する。
 */
function createWindow () {
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 870,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true, // contextIsolationを有効にする
      nodeIntegration: false // nodeIntegrationは無効にする
    }
  })

  mainWindow.setMenu(null) // ★追加: メニューバーを完全に無効化する
  mainWindow.loadFile('index.html')
  // 開発ツールを開く (デバッグ用)
  // mainWindow.webContents.openDevTools();
}

/**
 * 新しい対戦記録をデータベースに追加する。
 * @param {object} newMatch - 追加する対戦記録オブジェクト。
 * @returns {Promise<Array<object>>} 更新後のすべての対戦記録。
 */
async function addMatchToDb (newMatch) {
  try {
    await db.read()
    db.data.matches.unshift(newMatch) // 配列の先頭に追加
    await db.write()
    return db.data.matches
  } catch (error) {
    console.error('DBへの対戦記録追加中にエラー:', error)
    throw error // エラーを呼び出し元に再スローして、レンダラー側に失敗を伝える
  }
}

/**
 * データベースからすべての対戦記録を取得する。
 * @returns {Promise<Array<object>>} すべての対戦記録の配列。
 */
async function getMatchesFromDb () {
  try {
    await db.read()
    return db.data.matches || [] // データがない場合や読み込めない場合も空配列を返す
  } catch (error) {
    console.error('DBからの対戦記録取得中にエラー:', error)
    // エラーが発生した場合は空の配列を返して、UIがクラッシュしないようにする
    return []
  }
}

/**
 * 現在の対戦記録をCSVファイルとしてエクスポートする。
 * ユーザーに保存ダイアログを表示し、指定された場所にファイルを書き込む。
 * @returns {Promise<boolean>} エクスポートが成功した場合はtrue、失敗またはキャンセルされた場合はfalse。
 */
async function exportCsv () {
  await db.read()
  const matches = db.data.matches

  if (matches.length === 0) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'CSVエクスポート',
      message: 'エクスポートするデータがないよ。'
    })
    return false
  }

  const csvRows = []
  // CSVヘッダー（相手名を最後に追加）
  const headers = ['日時', '使用機体', '相手機体', 'ステージ', '勝敗', 'ラウンド', '相手名', 'ID']
  csvRows.push(headers.map(h => `"${h}"`).join(','))

  matches.forEach(match => {
    // 日時のフォーマットを対戦記録一覧と合わせる
    const date = new Date(match.id)
    const formattedDate = date.toLocaleString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/[/]/g, '/').replace(',', '')

    // CSVに含めるデータは文字列として処理し、常にダブルクォートで囲む
    const escapeCsv = (data) => {
      if (data === null || data === undefined) {
        return '""' // nullやundefinedは空のクォート付き文字列にする
      }
      const str = String(data)
      // 文字列内のダブルクォートを2つのダブルクォートにエスケープし、全体をダブルクォートで囲む
      const escapedStr = str.replace(/"/g, '""')
      return `"${escapedStr}"`
    }

    csvRows.push([
      escapeCsv(formattedDate),
      escapeCsv(match.myMachine),
      escapeCsv(match.opponentMachine),
      escapeCsv(match.stage),
      escapeCsv(match.result),
      escapeCsv(match.rounds || ''), // roundsがnullの場合は空文字列
      escapeCsv(match.opponentName || ''), // opponentNameがnullの場合は空文字列
      escapeCsv(match.id) // IDを最後に追加
    ].join(','))
  })

  const csvContent = csvRows.join('\n')

  const { filePath } = await dialog.showSaveDialog(mainWindow, {
    title: 'CSVファイルを保存',
    defaultPath: path.join(app.getPath('documents'), 'virtuaroid_match_records.csv'),
    filters: [
      { name: 'CSV Files', extensions: ['csv'] }
    ]
  })

  if (filePath) {
    try {
      // ★修正: UTF-8の文字列をShift_JISのバッファに変換して書き込む
      const sjisCsvBuffer = iconv.encode(csvContent, 'shiftjis')
      await fsp.writeFile(filePath, sjisCsvBuffer)
      return true
    } catch (error) {
      console.error('CSVファイルの保存中にエラーが発生しました:', error)
      dialog.showMessageBox(mainWindow, { // ★修正: エラーメッセージを柔らかく
        type: 'error',
        title: 'ごめんね！CSVエクスポートエラー',
        message: `CSVファイルの保存に失敗しちゃったみたい…\n\nエラー: ${error.message}`
      })
      return false
    }
  } else {
    return false // キャンセルされた場合
  }
}

/**
 * ★追加: 指定されたIDの対戦記録をデータベースから削除する。
 * @param {number} matchId - 削除する対戦記録のID。
 * @returns {Promise<boolean>} 削除が成功した場合はtrue、失敗した場合はfalse。
 */
async function deleteMatchFromDb (matchId) {
  try {
    await db.read()
    // 指定されたID以外の記録で新しい配列を作り、データを更新する
    db.data.matches = db.data.matches.filter(match => match.id !== matchId)
    await db.write()
    return true
  } catch (error) {
    console.error('DBからの対戦記録削除中にエラー:', error)
    return false
  }
}

/**
 * データベースからすべての対戦記録を削除する。
 * @returns {Promise<boolean>} 削除が成功した場合はtrue、失敗した場合はfalse。
 */
async function deleteAllRecordsFromDb () {
  try {
    await db.read()
    db.data.matches = [] // 空の配列にする
    await db.write()
    return true
  } catch (error) {
    console.error('DBの全記録削除中にエラー:', error)
    return false // 失敗したことを示す
  }
}

// --- Electronアプリのライフサイクル ---
app.whenReady().then(async () => {
  await initializeDb() // DBを初期化
  createWindow() // ウィンドウを作成

  // レンダラーの準備が完了してからファイル監視を開始する
  // これにより、初期ステータスの送信が確実に行われる
  mainWindow.webContents.on('did-finish-load', () => {
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// レンダラープロセスからのIPC通信ハンドラー

// 対戦記録を追加
ipcMain.handle('add-match', async (event, newMatch) => {
  return await addMatchToDb(newMatch)
})

// 全ての対戦記録を取得
ipcMain.handle('get-matches', async () => {
  return await getMatchesFromDb()
})

// CSVエクスポート
ipcMain.handle('export-csv', async () => {
  return await exportCsv()
})

// ★追加: 特定の対戦記録を削除
ipcMain.handle('delete-match', async (event, matchId) => {
  return await deleteMatchFromDb(matchId)
})

// 全ての対戦記録を削除
ipcMain.handle('delete-all-records', async () => {
  return await deleteAllRecordsFromDb()
})

// 確認ダイアログのIPCハンドラー
// 最初の削除確認ダイアログ
ipcMain.handle('confirm-first-delete', async () => {
  const result = await dialog.showMessageBox(mainWindow, { // mainWindowを使う
    type: 'warning',
    title: '確認',
    message: '本当にすべての記録を削除する？この操作は元に戻せないよ！',
    buttons: ['はい', 'いいえ'],
    defaultId: 1 // いいえをデフォルトにする
  })
  return result.response === 0 // 「はい」が押されたらtrue
})

// 最終確認ダイアログ
ipcMain.handle('confirm-final-delete', async () => {
  const result = await dialog.showMessageBox(mainWindow, { // mainWindowを使う
    type: 'warning',
    title: '最終確認',
    message: '本当に本当に、すべての記録を削除してもいい？最後のチャンスだよ！',
    buttons: ['はい、削除します', 'やっぱりやめます'],
    defaultId: 1 // やっぱりやめますをデフォルトにする
  })
  return result.response === 0 // 「はい、削除します」が押されたらtrue
})

// ★追加: 登録内容の確認ダイアログ
ipcMain.handle('show-confirm-dialog', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, {
    type: 'question',
    title: options.title || '確認',
    message: options.message || 'よろしいですか？',
    detail: options.detail || '',
    buttons: options.buttons || ['はい', 'いいえ'],
    defaultId: options.defaultId ?? 0,
    cancelId: options.cancelId ?? 1
  })
  // "はい" (index 0) が押されたら true を返す
  return result.response === 0
})

/**
 * ウィンドウを強制的に再アクティブ化し、再描画をトリガーする。
 * レンダリングの問題で入力フィールドにフォーカスできなくなる現象を回避するために使用する。
 */
ipcMain.handle('force-reactivate-window', () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore() // 最小化されていたら元に戻す
    }
    // レンダリングの問題で入力フィールドにフォーカスが当たらなくなることがあるため、
    // ウィンドウを一度非表示→表示することで強制的に再描画をトリガーする
    mainWindow.hide()
    mainWindow.show()
  }
})
