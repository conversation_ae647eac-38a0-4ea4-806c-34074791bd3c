# Virtual-ON OMG Record App

電脳戦機バーチャロン オラトリオ・タングラム (OMG) の対戦記録を自動で取り込み、管理・分析するためのデスクトップアプリケーションです。ASS (Arcade Stick System) から出力される対戦データを活用し、個人の戦績を詳細に把握できます。

## ✨ 主な機能

-   **自動対戦記録**: ASSが出力するJSONファイルを監視し、自動で対戦記録をデータベースに登録します。
-   **手動記録**: フォームから手動で対戦記録を入力・追加できます。
-   **戦績一覧**: 最新100件の対戦記録を一覧で表示します。
-   **直近戦績サマリー**: 直近25戦の勝敗を〇✕△で視覚的に表示し、勝敗数をカウントします。
-   **詳細分析**:
    -   全体の勝率
    -   使用機体別勝率
    -   相手機体別勝率
    -   ステージ別勝率
    -   ラウンド取得状況分析
    -   使用機体 × 相手機体別勝率
    -   対戦相手別勝率
-   **CSVエクスポート**: 記録された全対戦データをCSV形式でエクスポートできます。
-   **全記録削除**: データベース内の全対戦記録を削除できます（二段階確認あり）。

## 🚀 インストール方法 (ユーザー向け)

1.  **GitHub Releases からダウンロード**:
    最新のインストーラー (`Virtual-ON OMG Record App Setup X.X.X.exe` のようなファイル名) をダウンロードしてください。
    GitHub Releases ページ (https://github.com/osero2000/virtualon-omg-record-app/releases)

2.  **インストーラーの実行**:
    ダウンロードした `.exe` ファイルを実行し、画面の指示に従ってインストールを完了してください。

3.  **アプリの起動**:
    インストール後、スタートメニューから「Virtual-ON OMG Record App」を起動できます。

## 🎮 使い方

### ASS (Arcade Stick System) との連携

1.  **アプリを起動**:
    「Virtual-ON OMG Record App」を起動します。

2.  **監視フォルダの確認**:
    アプリのデータフォルダ内に `match_data_in` というフォルダが自動生成されます。
    (例: `C:\Users\<USER>\AppData\Roaming\Virtual-ON OMG Record App\match_data_in`)

3.  **ASSの設定**:
    ASS (Arcade Stick System) の設定で、対戦結果のJSON出力先をこの `match_data_in` フォルダに指定してください。

4.  **自動登録**:
    アプリが自動的に `match_data_in` フォルダ内の新しいJSONファイルを検知し、データベースに登録します。
    -   **「自動DB登録」チェックボックス**: このチェックを外すと、JSONデータがフォームに反映されるだけで、手動で登録するか選択できるようになります。

### 手動での対戦記録入力

1.  **「対戦記録入力」セクションへ移動**:
    アプリのメイン画面にある入力フォームを使用します。

2.  **各項目を入力**:
    「使用機体」「相手機体」「ステージ」「勝敗」「ラウンド」「対戦相手名」などの項目を選択・入力します。

3.  **記録の追加**:
    「記録を追加」ボタンをクリックすると、入力されたデータがデータベースに保存されます。

### 戦績の確認と分析

-   **「直近25戦の戦績」**: アプリ上部で、直近の勝敗を〇✕△で視覚的に確認できます。
-   **「対戦記録一覧」**: 最新100件の対戦記録がテーブル形式で表示されます。
-   **「分析」タブ**: 様々な角度からの詳細な戦績分析（機体別勝率、ステージ別勝率、ラウンド状況など）を確認できます。

### データ管理

-   **CSVエクスポート**:
    「CSVエクスポート」ボタンをクリックすると、記録された全対戦データをCSVファイルとして保存できます。

-   **全記録削除**:
    「全記録削除」ボタンをクリックすると、データベース内のすべての対戦記録を削除できます。この操作は元に戻せないので、実行前に二段階の確認ダイアログが表示されます。

## 🛠️ 開発者向け

### 前提条件

-   Node.js (v18以上推奨)
-   npm (Node.jsに付属)

### セットアップ

```bash
git clone https://github.com/osero2000/virtualon-omg-record-app.git
cd virtualon-omg-record-app
npm install
```

### 開発モードで実行

```bash
npm start
```

### アプリケーションのビルド

```bash
npm run build
```
ビルドされたインストーラーは、プロジェクトルートの `dist` フォルダ内に生成されます。

## 👤 作者

-   EcreChannel

## 📄 ライセンス

このプロジェクトは ISC License の下で公開されています。

---
