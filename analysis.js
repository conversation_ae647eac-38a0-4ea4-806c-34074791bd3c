import { MACHINES, STAGES, ROUND_OPTIONS_FOR_ANALYSIS } from './constants.js'
import { normalizeResult, createTable } from './utils.js' // createTableをimport

// ★★★ グローバル変数として定義されているUI要素を引数で受け取るように変更 ★★★
// これにより、analysis.jsがrenderer.jsのUI要素に直接依存しなくなる

/**
 * DBから最新の対戦記録を取得し、すべての分析（全体勝率、機体別勝率など）を再計算して表示を更新する。
 * @param {Array<object>} matches - 全対戦記録。
 * @param {HTMLElement} overallWinrateElement - 全体勝率を表示するHTML要素。
 * @param {HTMLElement} myMachineWinrateElement - 使用機体別勝率を表示するHTML要素。
 * @param {HTMLElement} opponentMachineWinrateElement - 相手機体別勝率を表示するHTML要素。
 * @param {HTMLElement} stageWinrateElement - ステージ別勝率を表示するHTML要素。
 * @param {HTMLElement} roundsAnalysisElement - ラウンド分析を表示するHTML要素。
 * @param {HTMLElement} myMachineOpponentAnalysisElement - 使用機体×相手機体別勝率を表示するHTML要素。
 * @param {HTMLElement} opponentNameWinrateElement - 対戦相手別勝率を表示するHTML要素。
 * @param {HTMLElement} myMachineStageWinrateElement - 使用機体ごとのステージ別勝率を表示するHTML要素。
 */
export function updateAnalysis (matches, overallWinrateElement, myMachineWinrateElement, opponentMachineWinrateElement, stageWinrateElement, roundsAnalysisElement, myMachineOpponentAnalysisElement, opponentNameWinrateElement, myMachineStageWinrateElement) {
  const totalMatches = matches.length
  const wins = matches.filter(m => normalizeResult(m.result) === 'WIN').length
  const losses = totalMatches - wins

  const overallWinRate = totalMatches > 0 ? (wins / totalMatches * 100).toFixed(2) : 0

  if (overallWinrateElement) {
    overallWinrateElement.innerHTML = `全体の勝率: <strong>${wins}勝&nbsp;${losses}敗</strong> (<strong>${overallWinRate}%</strong>)`
  }

  displayCategoryWinRate(matches, 'myMachine', myMachineWinrateElement, '使用機体別')
  displayCategoryWinRate(matches, 'opponentMachine', opponentMachineWinrateElement, '対戦相手機体別')
  displayCategoryWinRate(matches, 'stage', stageWinrateElement, 'ステージ別')

  displayRoundsAnalysis(matches, roundsAnalysisElement, 'ラウンド別')
  displayTwoCategoryWinRate(matches, 'myMachine', 'opponentMachine', myMachineOpponentAnalysisElement, '使用機体 × 相手機体別')
  displayCategoryWinRate(matches, 'opponentName', opponentNameWinrateElement, '対戦相手別')

  displayMyMachineStageWinRate(matches, myMachineStageWinrateElement)
}

/**
 * カテゴリごとの勝率を計算し、テーブルとして表示する汎用ヘルパー関数。
 * (使用機体別、相手機体別、ステージ別などで利用)
 * @param {Array<object>} matches - 全対戦記録。
 * @param {string} categoryKey - 分析の軸となるキー ('myMachine', 'stage'など)。
 * @param {HTMLElement} targetElement - 結果を表示するHTML要素。
 * @param {string} title - 表示するテーブルのタイトル。
 */
function displayCategoryWinRate (matches, categoryKey, targetElement, title) {
  const categoryStats = {}
  matches.forEach(match => {
    const category = match[categoryKey]
    if (categoryKey === 'opponentName' && (!category || category.trim() === '')) {
      return
    }
    if (!categoryStats[category]) {
      categoryStats[category] = { wins: 0, total: 0, loses: 0 }
    }
    categoryStats[category].total++
    const normalizedResult = normalizeResult(match.result)
    if (normalizedResult === 'WIN') {
      categoryStats[category].wins++
    } else if (normalizedResult === 'LOSE') {
      categoryStats[category].loses++
    }
  })

  if (!targetElement) return

  if (Object.keys(categoryStats).length === 0) {
    targetElement.innerHTML = `<h3>${title}勝率:</h3><p>まだデータがないよ。</p>`
    return
  }

  const headers = categoryKey === 'opponentName'
    ? ['対戦相手', '勝利数', '敗北数', '勝率', '総試合数']
    : [title.replace('別', ''), '勝利数', '敗北数', '勝率', '総試合数']
  const sortedCategories = Object.keys(categoryStats).sort((a, b) => {
    const statsA = categoryStats[a]
    const statsB = categoryStats[b]

    const winRateA = statsA.total > 0 ? (statsA.wins / statsA.total) : 0
    const winRateB = statsB.total > 0 ? (statsB.wins / statsB.total) : 0

    if (winRateA !== winRateB) {
      return winRateB - winRateA
    }

    let primarySortOrder = []
    if (categoryKey === 'myMachine' || categoryKey === 'opponentMachine') {
      primarySortOrder = MACHINES
    } else if (categoryKey === 'stage') {
      primarySortOrder = STAGES
    } else if (categoryKey === 'opponentName') {
      return a.localeCompare(b)
    }

    const indexA = primarySortOrder.indexOf(a)
    const indexB = primarySortOrder.indexOf(b)

    if (indexA === -1 || indexB === -1) {
      return a.localeCompare(b)
    }

    return indexA - indexB
  })
  const tableData = sortedCategories.map(category => {
    const stats = categoryStats[category]
    const winRate = stats.total > 0 ? (stats.wins / stats.total * 100).toFixed(2) : 0
    return [category, stats.wins, stats.loses, `${winRate}%`, stats.total]
  })

  const table = createTable(headers, tableData, 'analysis-table')
  targetElement.innerHTML = `<h3>${title}勝率:</h3>`
  targetElement.appendChild(table)
}

/**
 * 分析セクション（タイトルとテーブルまたはメッセージ）をターゲット要素に追加するヘルパー関数。
 * @param {HTMLElement} targetElement - 追加先の親要素。
 * @param {string} titleText - セクションのタイトル（h4）。
 * @param {Array<string>} headers - テーブルのヘッダー。
 * @param {Array<Array<string|number>>} data - テーブルのデータ。
 * @param {string} noDataMessage - データがない場合に表示するメッセージ。
 */
function appendAnalysisSection (targetElement, titleText, headers, data, noDataMessage) {
  targetElement.appendChild(document.createElement('h4')).textContent = titleText

  if (data.length > 0) {
    const table = createTable(headers, data, 'analysis-table')
    targetElement.appendChild(table)
  } else {
    const p = document.createElement('p')
    p.textContent = noDataMessage
    targetElement.appendChild(p)
  }
}
/**
 * ラウンド取得状況（ストレート勝ちの割合など）を分析し、表示する。
 * @param {Array<object>} matches - 全対戦記録。
 * @param {HTMLElement} targetElement - 結果を表示するHTML要素。
 * @param {string} title - 表示するセクションのタイトル。
 */
function displayRoundsAnalysis (matches, targetElement, title) {
  const overallRoundCounts = {}
  const winRoundCounts = {}
  const loseRoundCounts = {}
  const drawRoundCounts = {}

  let totalRecordedRounds = 0
  let totalWinsWithValidRounds = 0
  let totalLossesWithValidRounds = 0

  matches.forEach(match => {
    if (match.rounds) {
      const roundString = match.rounds

      totalRecordedRounds++
      overallRoundCounts[roundString] = (overallRoundCounts[roundString] || 0) + 1

      const normalizedResult = normalizeResult(match.result)
      if (normalizedResult === 'WIN') {
        if (roundString === '2 vs 0' || roundString === '2 vs 1') {
          totalWinsWithValidRounds++
          winRoundCounts[roundString] = (winRoundCounts[roundString] || 0) + 1
        }
      } else if (normalizedResult === 'LOSE') {
        if (roundString === '1 vs 2' || roundString === '0 vs 2') {
          totalLossesWithValidRounds++
          loseRoundCounts[roundString] = (loseRoundCounts[roundString] || 0) + 1
        }
      } else if (normalizedResult === 'DRAW') {
        if (roundString === 'DRAW') {
          drawRoundCounts[roundString] = (drawRoundCounts[roundString] || 0) + 1
        }
      }
    }
  })

  if (!targetElement) return

  targetElement.innerHTML = `<h3>${title}割合:</h3>`

  if (totalRecordedRounds === 0) {
    const p = document.createElement('p')
    p.textContent = 'まだラウンドのデータがないよ。'
    targetElement.appendChild(p)
    return // データがない場合はここで終了
  }

  // 各ラウンドの割合テーブル
  const overallHeaders = ['ラウンド', '試合数', '割合']
  const overallData = ROUND_OPTIONS_FOR_ANALYSIS.map(round => {
    const count = overallRoundCounts[round] || 0
    const percentage = (count / totalRecordedRounds * 100).toFixed(2)
    return [round, count, `${percentage}%`]
  })
  appendAnalysisSection(targetElement, '各ラウンドの割合:', overallHeaders, overallData, 'データがないみたい。')

  // WIN時のラウンド割合テーブル
  const winHeaders = ['ラウンド', '勝利数', '割合']
  const winData = (totalWinsWithValidRounds > 0)
    ? ['2 vs 0', '2 vs 1'].map(round => {
        const count = winRoundCounts[round] || 0
        const rate = (count / totalWinsWithValidRounds * 100).toFixed(2)
        return [round, count, `${rate}%`]
      })
    : []
  appendAnalysisSection(targetElement, 'WIN時のラウンド割合:', winHeaders, winData, 'WIN時のラウンドデータがないよ。')

  // LOSE時のラウンド割合テーブル
  const loseHeaders = ['ラウンド', '敗北数', '割合']
  const loseData = (totalLossesWithValidRounds > 0)
    ? ['1 vs 2', '0 vs 2'].map(round => {
        const count = loseRoundCounts[round] || 0
        const rate = (count / totalLossesWithValidRounds * 100).toFixed(2)
        return [round, count, `${rate}%`]
      })
    : []
  appendAnalysisSection(targetElement, 'LOSE時のラウンド割合:', loseHeaders, loseData, 'LOSE時のラウンドデータがないよ。')
}

/**
 * 2つのカテゴリを組み合わせたクロス集計の勝率を計算し、表示する。
 * (例: 使用機体ごと × 相手機体ごとの勝率)
 * @param {Array<object>} matches - 全対戦記録。
 * @param {string} primaryCategoryKey - 1つ目の軸となるキー ('myMachine'など)。
 * @param {string} secondaryCategoryKey - 2つ目の軸となるキー ('opponentMachine'など)。
 * @param {HTMLElement} targetElement - 結果を表示するHTML要素。
 * @param {string} title - 表示するセクションのタイトル。
 */
function displayTwoCategoryWinRate (matches, primaryCategoryKey, secondaryCategoryKey, targetElement, title) {
  const stats = {}

  matches.forEach(match => {
    const primary = match[primaryCategoryKey]
    const secondary = match[secondaryCategoryKey]

    if (!primary || !secondary) return

    if (!stats[primary]) {
      stats[primary] = {}
    }
    if (!stats[primary][secondary]) {
      stats[primary][secondary] = { wins: 0, total: 0 }
    }
    stats[primary][secondary].total++
    const normalizedResult = normalizeResult(match.result)
    if (normalizedResult === 'WIN') {
      stats[primary][secondary].wins++
    }
  })

  if (!targetElement) return

  // 表示エリアをクリアし、タイトルを設定
  targetElement.innerHTML = `<h3>${title}勝率:</h3>`

  if (Object.keys(stats).length === 0) {
    const p = document.createElement('p')
    p.textContent = 'まだデータがないよ。'
    targetElement.appendChild(p)
  } else {
    const sortedPrimaryCategories = Object.keys(stats).sort((a, b) => {
      const indexA = MACHINES.indexOf(a)
      const indexB = MACHINES.indexOf(b)
      if (indexA === -1 || indexB === -1) return a.localeCompare(b)
      return indexA - indexB
    })

    sortedPrimaryCategories.forEach(primaryCategory => {
      targetElement.appendChild(document.createElement('h4')).textContent = `${primaryCategory}:`

      const headers = [title.replace('別', '').split('×')[1], '勝利数', '敗北数', '勝率', '総試合数']
      const tableData = []

      const sortedSecondaryCategories = Object.keys(stats[primaryCategory]).sort((a, b) => {
        const indexA = MACHINES.indexOf(a)
        const indexB = MACHINES.indexOf(b)
        if (indexA === -1 || indexB === -1) return a.localeCompare(b)
        return indexA - indexB
      })

      sortedSecondaryCategories.forEach(secondaryCategory => {
        const categoryStats = stats[primaryCategory][secondaryCategory]
        const wins = categoryStats.wins
        const losses = categoryStats.total - categoryStats.wins
        const total = categoryStats.total
        const winRate = total > 0 ? (wins / total * 100).toFixed(2) : 0
        tableData.push([secondaryCategory, wins, losses, `${winRate}%`, total])
      })
      targetElement.appendChild(createTable(headers, tableData, 'analysis-table'))
    })
  }
}

/**
 * 使用機体ごとのステージ別勝率を計算し、表示する。
 * @param {Array<object>} matches - 全対戦記録。
 * @param {HTMLElement} analysisElement - 結果を表示するHTML要素。
 */
function displayMyMachineStageWinRate (matches, analysisElement) {
  if (!analysisElement) return

  // 表示エリアをクリアし、タイトルを設定
  analysisElement.innerHTML = '<h3>使用機体ごとのステージ別勝率:</h3>'

  const myMachineStats = {}
  matches.forEach(match => {
    const myMachine = match.myMachine
    const stage = match.stage

    if (!myMachine || !stage) return

    if (!myMachineStats[myMachine]) {
      myMachineStats[myMachine] = {}
    }

    if (!myMachineStats[myMachine][stage]) {
      myMachineStats[myMachine][stage] = { wins: 0, total: 0 }
    }

    myMachineStats[myMachine][stage].total++
    const normalizedResult = normalizeResult(match.result)
    if (normalizedResult === 'WIN') {
      myMachineStats[myMachine][stage].wins++
    }
  })

  if (Object.keys(myMachineStats).length === 0) {
    const p = document.createElement('p')
    p.textContent = 'まだデータがないよ。'
    analysisElement.appendChild(p)
  } else {
    MACHINES.forEach(machine => {
      if (myMachineStats[machine]) {
        analysisElement.appendChild(document.createElement('h4')).textContent = `${machine}:`

        const headers = ['ステージ', '勝利数', '敗北数', '勝率', '総試合数']
        const tableData = []

        STAGES.forEach(stage => {
          if (myMachineStats[machine][stage]) {
            const stats = myMachineStats[machine][stage]
            const wins = stats.wins
            const losses = stats.total - stats.wins
            const total = stats.total
            const winRate = total > 0 ? (wins / total * 100).toFixed(2) : 0

            tableData.push([stage, wins, losses, `${winRate}%`, total])
          }
        })
        analysisElement.appendChild(createTable(headers, tableData, 'analysis-table'))
      }
    })
  }
}
