# このCIワークフローの名前
name: CI

# このワークフローが実行されるタイミングを指定
on:
  # mainブランチにpushされたとき
  push:
    branches: [ main ]
  # mainブランチへのプルリクエストが作られたとき
  pull_request:
    branches: [ main ]

# 実行するジョブを定義
jobs:
  # ジョブの名前（なんでもOK）
  build-on-windows:
    # 実行するOSを指定（Windowsアプリなのでwindows-latestがオススメ）
    runs-on: windows-latest

    # ジョブのステップを定義
    steps:
      # 1. リポジトリのコードをチェックアウトしてくる
      - name: 1. Checkout code
        uses: actions/checkout@v4

      # 2. Node.jsの環境をセットアップする
      - name: 2. Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18' # package.jsonで推奨されてるバージョン
          cache: 'npm' # npmのキャッシュを有効にして高速化

      # 3. 依存パッケージをインストールする
      - name: 3. Install dependencies
        run: npm install

      # 4. アプリケーションをビルドする
      - name: 4. Build application
        run: npm run build

      # 5. ESLintでコードをチェックする
      - name: 5. Run ESLint
        run: npm run lint