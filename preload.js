const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('api', {
    getMatches: () => ipcRenderer.invoke('get-matches'),
    addMatch: (matchData) => ipcRenderer.invoke('add-match', matchData),
    exportCsv: () => ipcRenderer.invoke('export-csv'),
    
    // 全削除機能のための関数
    confirmFirstDelete: () => ipcRenderer.invoke('confirmFirstDelete'),
    confirmFinalDelete: () => ipcRenderer.invoke('confirmFinalDelete'),
    deleteAllRecords: () => ipcRenderer.invoke('deleteAllRecords'),

    forceReactivateWindow: () => ipcRenderer.invoke('forceReactivateWindow'), 

    // ★★★ ファイル監視関連のイベントリスナー ★★★
    onNewMatchData: (callback) => ipcRenderer.on('new-match-data', (_event, data) => callback(data)),
    onFileProcessingError: (callback) => ipcRenderer.on('file-processing-error', (_event, message) => callback(message)),
    onWatcherError: (callback) => ipcRenderer.on('watcher-error', (_event, message) => callback(message))
});
