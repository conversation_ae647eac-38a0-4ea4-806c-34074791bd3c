const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

contextBridge.exposeInMainWorld('api', {
  getMatches: () => ipcRenderer.invoke('get-matches'),
  addMatch: (matchData) => ipcRenderer.invoke('add-match', matchData),
  exportCsv: () => ipcRenderer.invoke('export-csv'),
  deleteMatch: (matchId) => ipcRenderer.invoke('delete-match', matchId), // ★追加

  // 全削除機能のための関数 (main.jsのIPCハンドラ名と一致させる)
  confirmFirstDelete: () => ipcRenderer.invoke('confirm-first-delete'),
  confirmFinalDelete: () => ipcRenderer.invoke('confirm-final-delete'),
  deleteAllRecords: () => ipcRenderer.invoke('delete-all-records'),

  // ★追加: 登録確認ダイアログ
  showConfirmDialog: (options) => ipcRenderer.invoke('show-confirm-dialog', options),

  forceReactivateWindow: () => ipcRenderer.invoke('force-reactivate-window')
})
