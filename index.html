<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>電脳戦機バーチャロン"OMG" 対戦記録アプリ</title>
    <link rel="stylesheet" href="./style.css">
    <!-- styleタグのコンテンツを削除 -->
</head>
<body>
    <!-- container divは削除 -->
    <h1>電脳戦機バーチャロン"OMG" 対戦記録アプリ</h1>

    <!-- ファイル処理のステータスとメッセージを表示 -->
    <p>処理ステータス: <span id="status">準備中</span></p>
    <p>メッセージ: <span id="message">ファイル監視を開始しています。</span></p>

    <!-- 自動DB登録チェックボックス -->
    <div class="settings-section">
        <label>
            <input type="checkbox" id="autoDbRegisterCheckbox">
            対戦結果を自動でDB登録する (ファイル取り込み後)
        </label>
    </div>

    <!-- ここから既存のレイアウト要素を維持 -->
    <div id="input-form">
        <h2>対戦記録入力</h2>
        <label for="myMachine">使用機体:</label>
        <select id="myMachine"></select><br>

        <label for="opponentMachine">対戦相手の機体:</label>
        <select id="opponentMachine"></select><br>

        <label for="stage">ステージ:</label>
        <select id="stage"></select><br>

        <label for="result">勝敗:</label>
        <select id="result"></select><br>

        <label for="rounds">取得ラウンド:</label>
        <select id="rounds"></select><br>

        <label for="opponentName">対戦相手名:</label>
        <input type="text"
               id="opponentName"
               list="opponentNamesList"
               placeholder="対戦相手名 (任意)"
               >
        <datalist id="opponentNamesList"></datalist><br>

        <button id="addRecordButton">記録を追加</button>
    </div>

    <hr>

    <div class="recent-matches-summary">
        <div class="summary-header">
            <h2>直近25戦<span id="recentWinLossCount"></span></h2>
            <button id="resetRecentMatchesButton" class="button">表示リセット</button>
        </div>
        <!--<p id="recentWinLossCount" class="recent-count-display"></p>--> <!-- このpタグはH2の中に移動されたため、HTMLではコメントアウトか削除が望ましい -->
        <div class="recent-marks-area">
            <div id="recentWinLossMarks" class="recent-marks-container"></div>
            <div class="recent-marks-guide"></div>
        </div>
    </div>

    <hr>

    <div id="analysis-section">
        <h2>勝敗分析</h2>
        <div id="overall-winrate"></div>
        <div id="my-machine-winrate"></div>
        <div id="opponent-machine-winrate"></div>
        <div id="stage-winrate"></div>
        <div id="rounds-analysis"></div>
        <div id="my-machine-opponent-analysis"></div>
        <div id="my-machine-stage-winrate"></div>
        <div id="opponent-name-winrate" class="scrollable-table-container"></div>
    </div>

    <hr>

    <div id="record-list-section">
        <h2>対戦記録一覧【最新100件】</h2>
        <table id="matches-table">
            <thead>
                <tr>
                    <th>日時</th>
                    <th>使用機体</th>
                    <th>相手機体</th>
                    <th>ステージ</th>
                    <th>勝敗</th>
                    <th>ラウンド</th>
                    <th>相手名</th>
                    <th>ID</th>
                </tr>
            </thead>
            <tbody>
                </tbody>
        </table>
        <p id="no-records-message" style="display: none;">まだ対戦記録がないよ。</p>
    </div>

    <hr>

    <div id="data-export-section">
        <h2>データ出力</h2>
        <button id="exportCsvButton">CSV出力</button>
    </div>

    <hr>

    <div id="data-delete-section">
        <h2>データ削除</h2>
        <button id="deleteAllRecordsButton" class="button button-danger">すべての対戦記録を削除</button>
    </div>

    <p class="app-version-info">---Virtual-ON "OMG" Match recording app--- -ver1.1.3- ---created by EcreChannel--- </p>

    <script src="./renderer.js"></script>
</body>
</html>
