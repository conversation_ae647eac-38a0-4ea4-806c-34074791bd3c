<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>電脳戦機バーチャロン"OMG" 対戦記録アプリ</title>
    <link rel="stylesheet" href="./style.css">
    <link rel="stylesheet" href="./focus-style.css"> <!-- ★追加: フォーカススタイル -->
</head>
<body>
    <!-- container divは削除 -->
    <h1>電脳戦機バーチャロン"OMG" 対戦記録アプリ</h1>

    <!-- メッセージを表示 -->
    <p id="message" class="app-message">ここにメッセージが表示されるよ</p>

    <!-- ★追加: 入力フォームとショートカットガイドを囲むコンテナ -->
    <div class="main-content-area">
        <!-- ここから既存のレイアウト要素を維持 -->
        <div id="input-form">
            <h2>対戦記録入力</h2>
            <label for="myMachine">使用機体:</label>
            <select id="myMachine"></select><span id="myMachineColor" class="machine-color-indicator"></span><br>

            <label for="opponentMachine">対戦相手の機体:</label>
            <select id="opponentMachine"></select><span id="opponentMachineColor" class="machine-color-indicator"></span><br>

            <label for="stage">ステージ:</label>
            <select id="stage"></select><br>

            <label for="result">勝敗/ラウンド:</label>
            <select id="result"></select><span id="resultColor" class="result-color-indicator"></span><br>

            <label for="opponentName">対戦相手名:</label>
            <input type="text"
                   id="opponentName"
                   placeholder="対戦相手名 (任意)"
                   autocomplete="off"
                   >
            <div id="opponentNameSuggestions" class="suggestions-container"></div><br>

            <button id="addRecordButton">記録を追加</button>
        </div>

        <!-- ★追加: ショートカットガイド -->
        <div id="shortcut-guide">
            <h2>ショートカット一覧</h2>
            <table class="shortcut-table">
                <thead>
                    <tr>
                        <th>操作</th>
                        <th>キー</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td class="category" colspan="2">項目選択</td></tr>
                    <tr><td>選択肢を上下に移動</td><td><code>Alt</code> + <code>W</code> / <code>S</code> (<code>A</code>/<code>D</code>)</td></tr>
                    <tr><td>選択肢を直接選ぶ</td><td><code>1</code>-<code>4</code> / <code>q</code><code>w</code><code>e</code><code>r</code></td></tr>
                    <tr><td class="category" colspan="2">相手名サジェスト</td></tr>
                    <tr><td>候補を上下に移動</td><td><code>Alt</code> + <code>W</code> / <code>S</code> (<code>A</code>/<code>D</code>)</td></tr>
                    <tr><td>候補を確定</td><td><code>Tab</code></td></tr>
                    <tr><td class="category" colspan="2">記録の追加</td></tr>
                    <tr><td>どこからでも追加</td><td><code>Alt</code> + <code>X</code></td></tr>
                    <tr><td>相手名入力欄で追加</td><td><code>Enter</code></td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="recent-matches-summary">
        <div class="summary-header">
            <h2>直近35戦の勝敗</h2>
            <button id="resetRecentMatchesButton" class="button">表示リセット</button>
        </div>
        <p id="recentWinLossCount" class="recent-count-display"></p>
        <div class="recent-marks-area">
            <div id="recentWinLossMarks" class="recent-marks-container"></div>
            <div class="recent-marks-guide"></div>
        </div>
    </div>

    <hr>

    <div id="analysis-section">
        <h2>勝敗分析</h2>
        <div id="overall-winrate"></div>
        <div id="my-machine-winrate"></div>
        <div id="opponent-machine-winrate"></div>
        <div id="stage-winrate"></div>
        <div id="rounds-analysis"></div>
        <div id="my-machine-opponent-analysis"></div>
        <div id="my-machine-stage-winrate"></div>
        <div id="opponent-name-winrate" class="scrollable-table-container"></div>
    </div>

    <hr>

    <div id="record-list-section">
        <h2>対戦記録一覧【最新100件】</h2>

        <!-- ★ここから追加 -->
        <div class="delete-by-id-container">
            <input type="text" id="deleteIdInput" placeholder="削除する記録のIDを入力">
            <button id="deleteByIdButton" class="button button-danger">指定IDを削除</button>
        </div>
        <!-- ★ここまで追加 -->        

        <table id="matches-table">
            <thead>
                <tr>
                    <th>日時</th>
                    <th>使用機体</th>
                    <th>相手機体</th>
                    <th>ステージ</th>
                    <th>勝敗</th>
                    <th>ラウンド</th>
                    <th>相手名</th>
                    <th>ID</th>
                </tr>
            </thead>
            <tbody>
                </tbody>
        </table>
        <p id="no-records-message" style="display: none;">まだ対戦記録がないよ。</p>
    </div>

    <hr>

    <div id="data-export-section">
        <h2>データ出力</h2>
        <button id="exportCsvButton">CSV出力</button>
    </div>

    <hr>

    <div id="data-delete-section">
        <h2>データ削除</h2>
        <button id="deleteAllRecordsButton" class="button button-danger">すべての対戦記録を削除</button>
    </div>

    <hr>

    <!-- ★追加: 設定セクションをアプリ下部に移動 -->
    <div id="settings-area">
        <h2>設定</h2>
        <div class="settings-section">
            <label>
                <input type="checkbox" id="confirmRegisterCheckbox">
                記録を追加する前に、内容確認のダイアグラムを表示する
            </label>
        </div>
        <div class="settings-section">
            <label>
                <input type="checkbox" id="soundEnabledCheckbox">
                操作時にサウンドを再生する
            </label>
        </div>
    </div>

    <p class="app-version-info">---Virtual-ON "OMG" Match recording app--- -ver1.6.0- ---created by EcreChannel--- </p>

    <script type="module" src="./renderer.js"></script>
</body>
</html>
