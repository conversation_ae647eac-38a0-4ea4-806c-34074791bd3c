// renderer.js

// obs-websocket-js ライブラリのインポートを削除

// OBS関連の変数とUIヘルパー関数を削除
// let obs = null;
// let isObsConnectedInternally = false;
// let statusSpan, messageSpan, connectButton, disconnectButton, getSceneButton, updateTextButton;
// function updateStatus(text, color = 'black') { ... }
// function updateMessage(text) { ... }
// function updateButtonStates() { ... }


// 接続と対戦記録の連携に関するUI要素 (既存のまま)
let myMachineSelect, opponentMachineSelect, stageSelect, resultSelect, roundsSelect, opponentNameInput, opponentNamesList, addRecordButton, exportCsvButton, deleteAllRecordsButton;
let matchesTableBody, noRecordsMessage;
let recentWinLossCountElement, recentWinLossMarksElement, resetRecentMatchesButton;
let autoDbRegisterCheckbox; // 自動DB登録チェックボックスはファイル連携で使うので残す

// UI更新ヘルパー関数 (OBS WebSocket関連の引数・ロジックを削除)
// ステータスとメッセージ表示は、ファイル処理のエラーや成功時に引き続き使用
function updateStatus(text, color = 'black') {
    // statusSpan が存在するかチェック
    const currentStatusSpan = document.getElementById('status');
    if (currentStatusSpan) {
        currentStatusSpan.textContent = text;
        currentStatusSpan.style.color = color;
    }
}

function updateMessage(text) {
    // messageSpan が存在するかチェック
    const currentMessageSpan = document.getElementById('message');
    if (currentMessageSpan) {
        currentMessageSpan.textContent = text;
    }
}

// ボタンの有効/無効状態を更新する関数は削除


// --- DOMContentLoadedイベントリスナー ---
document.addEventListener('DOMContentLoaded', () => {
    // HTML要素をここで取得する (OBS WebSocket関連のボタンは削除)
    // statusSpan = document.getElementById('status');
    // messageSpan = document.getElementById('message');
    // connectButton = document.getElementById('connectButton');
    // disconnectButton = document.getElementById('disconnectButton');
    // getSceneButton = document.getElementById('getSceneButton');
    // updateTextButton = document.getElementById('updateTextButton');

    // ★★★ 既存の対戦記録アプリのフォーム要素を取得 ★★★
    // DOMContentLoadedスコープ内で再取得するように修正
    myMachineSelect = document.getElementById('myMachine');
    opponentMachineSelect = document.getElementById('opponentMachine');
    stageSelect = document.getElementById('stage');
    resultSelect = document.getElementById('result');
    roundsSelect = document.getElementById('rounds');
    opponentNameInput = document.getElementById('opponentName');
    opponentNamesList = document.getElementById('opponentNamesList');
    addRecordButton = document.getElementById('addRecordButton');
    exportCsvButton = document.getElementById('exportCsvButton');
    deleteAllRecordsButton = document.getElementById('deleteAllRecordsButton');
    matchesTableBody = document.querySelector('#matches-table tbody');
    noRecordsMessage = document.getElementById('no-records-message');
    recentWinLossCountElement = document.getElementById('recentWinLossCount');
    recentWinLossMarksElement = document.getElementById('recentWinLossMarks');
    resetRecentMatchesButton = document.getElementById('resetRecentMatchesButton');
    statusSpan = document.getElementById('status'); // file-only版でも使うので再取得
    messageSpan = document.getElementById('message'); // file-only版でも使うので再取得


    // ★★★ 自動DB登録チェックボックスの要素を取得 ★★★
    autoDbRegisterCheckbox = document.getElementById('autoDbRegisterCheckbox');
    // localStorageから設定をロードし、チェックボックスの初期状態を反映
    const savedAutoRegisterSetting = localStorage.getItem('autoDbRegister');
    if (savedAutoRegisterSetting !== null) { 
        autoDbRegisterCheckbox.checked = (savedAutoRegisterSetting === 'true');
    } else { 
        autoDbRegisterCheckbox.checked = true;
        localStorage.setItem('autoDbRegister', 'true');
    }
    // チェックボックスの状態変更を監視し、localStorageに保存
    autoDbRegisterCheckbox.addEventListener('change', () => {
        localStorage.setItem('autoDbRegister', autoDbRegisterCheckbox.checked.toString());
        updateMessage(`自動DB登録モードが ${autoDbRegisterCheckbox.checked ? '有効' : '無効'} になりました。`);
    });


    // OBSWebSocketインスタンスの初期化を削除
    // obs = new OBSWebSocket(); 

    // アプリ起動時の初期ボタン状態設定を削除
    // updateButtonStates();
    console.log("Initial setup for file-only mode.");


    // --- OBS WebSocket 接続処理 (すべて削除) ---
    // connectButton.addEventListener('click', async () => { ... });
    // disconnectButton.addEventListener('click', async () => { ... });
    // getSceneButton.addEventListener('click', async () => { ... });
    // updateTextButton.addEventListener('click', async () => { ... });

    // --- OBSからのイベント購読 (すべて削除) ---
    // obs.on('ConnectionOpened', () => { ... });
    // obs.on('Identified', () => { ... });
    // obs.on('ConnectionClosed', () => { ... });
    // obs.on('ConnectionError', (error) => { ... });
    // obs.on('CurrentProgramSceneChanged', (data) => { ... });


    // --- ファイル監視関連のイベント購読 (メインプロセスから受信) ---
    window.api.onNewMatchData(async (assData) => {
        console.log('レンダラープロセスで新しい対戦データを受信しました:', assData);
        updateMessage(`新しい対戦データを受信しました: ${JSON.stringify(assData, null, 2).substring(0, 100)}...`);

        // ASSからのデータをLowDBに保存する形式にマッピング
        const mappedMatchData = mapAssDataToMatchData(assData);
        console.log('マッピング後の対戦データ:', mappedMatchData);

        // 自動DB登録モードのチェック
        if (autoDbRegisterCheckbox.checked) {
            try {
                await window.api.addMatch(mappedMatchData);
                updateMessage('対戦データを自動的にDBに登録しました！');
                console.log('対戦データを自動登録しました。');
                // UIを更新
                await updateAnalysis(); 
                await updateOpponentNames();
                await displayMatches(); 
            } catch (error) {
                console.error('自動DB登録エラー:', error);
                updateMessage(`自動DB登録中にエラーが発生しました: ${error.message}`, 'red');
            }
        } else {
            // 手動DB登録モードの場合、フォームにデータを反映
            updateMessage('新しい対戦データが来ました。手動でフォームを確認してください。');
            populateFormWithAssData(mappedMatchData);
            console.log('対戦データをフォームに反映しました。');
        }
    });

    window.api.onFileProcessingError((message) => {
        console.error('レンダラープロセスでファイル処理エラーを受信しました:', message);
        updateMessage(`ファイル処理エラー: ${message}`, 'red');
    });

    window.api.onWatcherError((message) => {
        console.error('レンダラープロセスでファイル監視エラーを受信しました:', message);
        updateMessage(`ファイル監視エラー: ${message}`, 'red');
    });


    // --- バーチャロン対戦記録アプリ 既存のロジック ---

    // 選択肢の定義
    const MACHINES = ['テムジン', 'ドルカス', 'フェイ・イェン', 'ベルグドル', 'バイパー２', 'バル・バス・バウ', 'アファームド', 'ライデン'];
    const STAGES = ['FLOODED CITY', 'AIRPORT', 'WATER FRONT', 'GREEN HILLS', 'RUINS', 'SPACE DOCK', 'MOON BASE', 'DEATH TRAP'];

// ★★★ ここを修正：勝敗とラウンドのオプションにオブジェクト形式でテキストと値を指定 ★★★
// プレースホルダーオプションを配列の先頭に追加
const RESULT_OPTIONS = [
    { value: '', text: '(必須)' }, // プレースホルダーのテキストと値
    { value: 'WIN', text: 'WIN' },
    { value: 'LOSE', text: 'LOSE' },
    { value: 'DRAW', text: 'DRAW' }
];
const ROUND_OPTIONS = [
    { value: '', text: '(任意)' }, // プレースホルダーのテキストと値
    { value: '2 vs 0', text: '2 vs 0' },
    { value: '2 vs 1', text: '2 vs 1' },
    { value: '1 vs 2', text: '1 vs 2' },
    { value: '0 vs 2', text: '0 vs 2' },
    { value: 'DRAW', text: 'DRAW' }
];

// セレクトボックスのオプションを生成するヘルパー関数
// ★★★ populateSelect 関数を修正：オプションがオブジェクト形式も受け入れられるようにする ★★★
function populateSelect(selectId, options) {
    const selectElement = document.getElementById(selectId);
    if (!selectElement) { // 要素が見つからない場合のガード句
        console.warn(`Element with ID '${selectId}' not found.`);
        return;
    }
    selectElement.innerHTML = ''; // 既存のオプションをクリア

    options.forEach(optionConfig => { // optionConfigは { value: '...', text: '...' } の形式
        const opt = document.createElement('option');
        if (typeof optionConfig === 'object' && optionConfig !== null && 'value' in optionConfig && 'text' in optionConfig) {
            opt.value = optionConfig.value;
            opt.textContent = optionConfig.text;
            if (optionConfig.value === '') { // プレースホルダーオプションの場合
                opt.disabled = true; // プレースホルダーは選択不可にする (ユーザーが誤って選択しないように)
                opt.selected = true; // デフォルトで選択状態にする
            }
        } else { // MACHINES, STAGESなど、文字列の配列を受け取る場合（既存互換）
            opt.value = optionConfig;
            opt.textContent = optionConfig;
        }
        selectElement.appendChild(opt);
    });

    // 初期ロード時と値変更時にプレースホルダーを示すクラスを切り替える
    const updatePlaceholderClass = () => {
        if (selectElement.value === '') {
            selectElement.classList.add('is-placeholder');
        } else {
            selectElement.classList.remove('is-placeholder');
        }
    };

    selectElement.addEventListener('change', updatePlaceholderClass);
    updatePlaceholderClass(); // 初期状態を設定
}

    // ASSからのデータをフォームに反映するヘルパー関数
    function populateFormWithAssData(matchData) {
        // nullチェックを追加
        if (myMachineSelect) myMachineSelect.value = matchData.myMachine || '';
        if (opponentMachineSelect) opponentMachineSelect.value = matchData.opponentMachine || '';
        if (stageSelect) stageSelect.value = matchData.stage || '';
        if (resultSelect) resultSelect.value = matchData.result || '';
        if (roundsSelect) roundsSelect.value = matchData.rounds || '';
        // opponentNameInput は手動入力なので触らない
        // opponentNamesList は既存のまま
    }

    // ASSから受信したデータ形式をLowDB保存形式にマッピングする関数
    function mapAssDataToMatchData(assData) {
        let resultMapping = 'UNKNOWN'; // デフォルト値
        if (assData.result === '勝利') {
            resultMapping = 'WIN';
        } else if (assData.result === '敗北') {
            resultMapping = 'LOSE';
        } else if (assData.result === '引き分け' || assData.result === 'DRAW') { 
            resultMapping = 'DRAW';
        }

        let roundsString = null;
        if (resultMapping === 'DRAW') {
            roundsString = 'DRAW';
        } else if (typeof assData.rounds_won === 'number' && typeof assData.rounds_lost === 'number') {
            // 例: { "rounds_won": 2, "rounds_lost": 0 } -> "2 vs 0"
            roundsString = `${assData.rounds_won} vs ${assData.rounds_lost}`;
        }

        return {
            id: Date.now(), // 新しいIDを生成
            myMachine: assData.my_character || '不明',
            opponentMachine: assData.opponent_character || '不明',
            stage: assData.stage || '不明',
            result: resultMapping,
            rounds: roundsString,
            opponentName: opponentNameInput.value.trim() || null // 手動入力の場合、フォームの現在の内容を使用
        };
    }


    // 初期データの読み込みとUIの初期化
    async function initializeUI() {
        populateSelect('myMachine', MACHINES);
        populateSelect('opponentMachine', MACHINES);
        populateSelect('stage', STAGES);
        populateSelect('result', RESULT_OPTIONS); // 勝敗セレクトボックスも初期化
        populateSelect('rounds', ROUND_OPTIONS); // ラウンドセレクトボックスも初期化

        // 前回入力した内容をロード
        loadLastInput(); 

        // ★★★ ここが重要！loadLastInput()で設定された後、改めてplaceholderクラスを更新する ★★★
        const selectElementsWithPlaceholder = [resultSelect, roundsSelect];
        selectElementsWithPlaceholder.forEach(selectElement => {
            if (selectElement) { // 要素が存在するかチェック
                if (selectElement.value === '') {
                    selectElement.classList.add('is-placeholder');
                } else {
                    selectElement.classList.remove('is-placeholder');
                }
            }
        });

        await updateAnalysis(); // 分析結果を初期表示
        await updateOpponentNames(); // 対戦相手名リストを初期表示
        await displayMatches(); // 記録一覧を初期表示
    }

    // 前回入力した内容をロードする関数
    function loadLastInput() {
        const lastMyMachine = localStorage.getItem('lastMyMachine');
        const lastOpponentMachine = localStorage.getItem('lastOpponentMachine');
        const lastStage = localStorage.getItem('lastStage');
        const lastResult = localStorage.getItem('lastResult'); // 勝敗もロード
        const lastRounds = localStorage.getItem('lastRounds'); // ラウンドもロード

        if (myMachineSelect && lastMyMachine) myMachineSelect.value = lastMyMachine;
        if (opponentMachineSelect && lastOpponentMachine) opponentMachineSelect.value = lastOpponentMachine;
        if (stageSelect && lastStage) stageSelect.value = lastStage;
        if (resultSelect && lastResult) resultSelect.value = lastResult; // 勝敗も反映
        if (roundsSelect && lastRounds) roundsSelect.value = lastRounds; // ラウンドも反映
    }

    // 対戦相手名リストを更新する関数
    async function updateOpponentNames() {
        const matches = await window.api.getMatches();
        
        // ユニークな名前のリストを作成
        const uniqueNames = [...new Set(matches.map(m => m.opponentName).filter(name => name))];
        
        opponentNamesList.innerHTML = ''; // クリア
        uniqueNames.forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            opponentNamesList.appendChild(option);
        });
    }

    // リセットボタンのイベントリスナー
    if (resetRecentMatchesButton) { // 要素が存在するかチェック
        resetRecentMatchesButton.addEventListener('click', () => {
            // ローカルストレージにリセットされたことを記録
            localStorage.setItem('recentMatchesResetTimestamp', Date.now().toString());
            
            // リセット後、表示を更新するためにdisplayMatchesを呼び出す
            displayMatches(); 
        });
    }

    // 直近25戦の概要を表示する関数
    // この関数は displayMatches の中で呼び出す
    const RECENT_MATCH_COUNT = 25; // 直近25戦
    const MARK_ITEM_TOTAL_WIDTH = 22; // .recent-mark の実質的な幅 (CSSと合わせる)

    function displayRecentMatchesSummary(wins, losses, marksArray) {
        if (!recentWinLossCountElement || !recentWinLossMarksElement) return; // 要素が存在しない場合のガード

        recentWinLossCountElement.textContent = `自分: ${wins}　vs　相手: ${losses}`;
        recentWinLossMarksElement.innerHTML = marksArray.join('');

        const recentMatchesSummaryDiv = document.querySelector('.recent-matches-summary');

        // 記録が全くない（すべて'-'）かつリセットされていない初期状態の場合のみ非表示にする
        const resetTimestamp = localStorage.getItem('recentMatchesResetTimestamp');
        const isInitialEmptyState = (wins === 0 && losses === 0 && marksArray.every(mark => mark.includes('none')) && !resetTimestamp);

        if (recentMatchesSummaryDiv) { // 要素が存在するかチェック
            if (isInitialEmptyState) {
                recentMatchesSummaryDiv.style.display = 'none';
            } else {
                recentMatchesSummaryDiv.style.display = 'block';
            }
        }

        // ガイドの数字を動的に生成
        const recentMarksGuideElement = recentMatchesSummaryDiv ? recentMatchesSummaryDiv.querySelector('.recent-marks-guide') : null;
        if (recentMarksGuideElement) { // 要素が存在するかチェック
            recentMarksGuideElement.innerHTML = ''; // クリア

            const GUIDE_NUMBERS = [5, 10, 15, 20, 25];

            GUIDE_NUMBERS.forEach(num => {
                if (num <= RECENT_MATCH_COUNT) {
                    const guideSpan = document.createElement('span');
                    guideSpan.textContent = num.toString();
                    
                    // 手動で調整したら5の位置で118pxでちょうどよかったのでそれに合わせてみる
                    guideSpan.style.left = `${(num / 5) * 115 + ((num / 5) + 2)}px`;

                    recentMarksGuideElement.appendChild(guideSpan);
                }
            });

            // 重要: ガイドの親要素の幅をマークコンテナの幅と同期させる（スクロールを合わせるため）
            recentMarksGuideElement.style.width = `${RECENT_MATCH_COUNT * MARK_ITEM_TOTAL_WIDTH}px`;
        }
        if (recentWinLossMarksElement) { // 要素が存在するかチェック
            recentWinLossMarksElement.style.width = `${RECENT_MATCH_COUNT * MARK_ITEM_TOTAL_WIDTH}px`; // マークコンテナも固定幅にする
        }
    }

    // 記録一覧を表示する関数 (最新100件に制限)
    async function displayMatches() {
        const allMatches = (await window.api.getMatches()) || []; // main.jsから全記録を取得

        if (!matchesTableBody || !noRecordsMessage) return; // 要素が存在しない場合のガード

        // テーブルのtbodyをクリア
        matchesTableBody.innerHTML = '';

        // 全データ削除後のクリア処理
        if (allMatches.length === 0) {
            localStorage.removeItem('recentMatchesResetTimestamp');
            displayRecentMatchesSummary(0, 0, Array(RECENT_MATCH_COUNT).fill('<div class="recent-mark none">-</div>'));
            const summaryDiv = document.querySelector('.recent-matches-summary');
            if (summaryDiv) summaryDiv.style.display = 'none'; // 明示的に非表示
            
            noRecordsMessage.style.display = 'block';
            matchesTableBody.style.display = 'none';
            return;
        } else {
            noRecordsMessage.style.display = 'none';
            matchesTableBody.style.display = 'table-row-group';

            let resetTimestamp = localStorage.getItem('recentMatchesResetTimestamp');
            let resetTime = resetTimestamp ? parseInt(resetTimestamp, 10) : 0;

            let wins = 0;
            let losses = 0;
            let draws = 0;
            
            const marks = Array(RECENT_MATCH_COUNT).fill('<div class="recent-mark none">-</div>');

            const relevantMatches = allMatches.filter(match => parseInt(match.id, 10) > resetTime);
            const matchesToDisplay = relevantMatches.slice(0, RECENT_MATCH_COUNT);
            const orderedMatchesForDisplay = [...matchesToDisplay].reverse();

            orderedMatchesForDisplay.forEach((match, index) => {
                if (match.result === 'WIN') {
                    marks[index] = '<div class="recent-mark win">〇</div>';
                    wins++;
                } else if (match.result === 'LOSE') {
                    marks[index] = '<div class="recent-mark lose">✕</div>';
                    losses++;
                } else if (match.result === 'DRAW') {
                    marks[index] = '<div class="recent-mark draw">△</div>';
                    draws++;
                }
            });
            
            displayRecentMatchesSummary(wins, losses, marks);

            const latestMatches = allMatches.slice(0, 100); 

            latestMatches.forEach(match => {
                const row = document.createElement('tr');
                
                const date = match.id ? new Date(match.id) : new Date(); 
                const formattedDate = date.toLocaleString('ja-JP', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                }).replace(/\//g, '/').replace(',', ''); 

                let resultClass = ''; 
                if (match.result === 'WIN') {
                    resultClass = 'result-win';
                } else if (match.result === 'LOSE') {
                    resultClass = 'result-lose';
                } else if (match.result === 'DRAW') {
                    resultClass = 'result-draw';
                } else {
                    resultClass = 'result-other';
                }

                row.innerHTML = `
                    <td>${formattedDate}</td>
                    <td>${match.myMachine}</td>
                    <td>${match.opponentMachine}</td>
                    <td>${match.stage}</td>
                    <td class="${resultClass}">${match.result}</td> <td>${match.rounds || '-'}</td>
                    <td>${match.opponentName || '-'}</td>
                    <td>${match.id}</td>
                `;
                
                matchesTableBody.appendChild(row);
            });
        }
    }

    // アプリのロード時に一度だけ initializeUI を呼び出す
    initializeUI();

    // 記録追加ボタンのイベントリスナー
    if (addRecordButton) { // 要素が存在するかチェック
        addRecordButton.addEventListener('click', async () => {
            const myMachine = myMachineSelect.value;
            const opponentMachine = opponentMachineSelect.value;
            const stage = stageSelect.value;
            const result = resultSelect.value;
            const rounds = roundsSelect.value;
            const opponentName = opponentNameInput.value.trim();

            if (!myMachine || !opponentMachine || !stage || !result) {
                alert('必須項目をすべて入力してね！');
                return;
            }

            const newMatch = {
                id: Date.now(), 
                myMachine,
                opponentMachine,
                stage,
                result,
                rounds: rounds || null, 
                opponentName: opponentName || null 
            };

            const updatedMatches = await window.api.addMatch(newMatch);

            alert('記録を追加したよ！');

            localStorage.setItem('lastMyMachine', myMachine);
            localStorage.setItem('lastOpponentMachine', opponentMachineSelect.value); 
            localStorage.setItem('lastStage', stage);
            // ★★★ ここから修正：登録後、勝敗とラウンドを未選択状態にリセット ★★★
            localStorage.setItem('lastResult', ''); // 空文字列を保存して次回ロード時に未選択に
            localStorage.setItem('lastRounds', ''); // 空文字列を保存して次回ロード時に未選択に
            
            resultSelect.value = ''; // フォームの値を未選択に
            roundsSelect.value = ''; // フォームの値を未選択に

            // selectElementのchangeイベントを手動でトリガー、または直接クラスを更新
            // populateSelect内でupdatePlaceholderClassがイベントリスナーとして設定されているので、
            // 直接.valueを設定するだけではchangeイベントは発火しない。
            // なので、明示的にupdatePlaceholderClassロジックを実行する。
            if (resultSelect) resultSelect.classList.add('is-placeholder');
            if (roundsSelect) roundsSelect.classList.add('is-placeholder');
            // ★★★ ここまで修正 ★★★

            await updateOpponentNames(); 
            await updateAnalysis();
            await displayMatches(); 
            
            opponentNameInput.value = '';

            setTimeout(async () => {
                await window.api.forceReactivateWindow(); 
                opponentMachineSelect.focus(); 
            }, 100); 
        });
    }

    // 分析を更新する関数
    async function updateAnalysis() {
        const matches = (await window.api.getMatches()) || []; 

        const totalMatches = matches.length; 
        const wins = matches.filter(m => m.result === 'WIN').length;
        const losses = totalMatches - wins; 

        const overallWinRate = totalMatches > 0 ? (wins / totalMatches * 100).toFixed(2) : 0;

        const overallWinrateElement = document.getElementById('overall-winrate');
        if (overallWinrateElement) { // 要素が存在するかチェック
            overallWinrateElement.innerHTML = `全体の勝率: <strong>${wins}勝 ${losses}敗</strong> (<strong>${overallWinRate}%</strong>)`;
        }

        displayCategoryWinRate(matches, 'myMachine', 'my-machine-winrate', '使用機体別');
        displayCategoryWinRate(matches, 'opponentMachine', 'opponent-machine-winrate', '対戦相手機体別');
        displayCategoryWinRate(matches, 'stage', 'stage-winrate', 'ステージ別');

        displayRoundsAnalysis(matches, 'rounds-analysis', 'ラウンド別');
        displayTwoCategoryWinRate(matches, 'myMachine', 'opponentMachine', 'my-machine-opponent-analysis', '使用機体 × 相手機体別');
        displayCategoryWinRate(matches, 'opponentName', 'opponent-name-winrate', '対戦相手別');

        displayMyMachineStageWinRate(matches);
    }

    // カテゴリごとの勝率を表示するヘルパー関数
    function displayCategoryWinRate(matches, categoryKey, elementId, title) {
        const categoryStats = {};
        matches.forEach(match => {
            const category = match[categoryKey];
            if (categoryKey === 'opponentName' && (!category || category.trim() === '')) {
                return; 
            }
            if (!categoryStats[category]) {
                categoryStats[category] = { wins: 0, total: 0, loses: 0, draws: 0 }; 
            }
            categoryStats[category].total++;
            if (match.result === 'WIN') {
                categoryStats[category].wins++;
            } else if (match.result === 'LOSE') { 
                categoryStats[category].loses++;
            } else if (match.result === 'DRAW') { 
                categoryStats[category].draws++;
            }
        });

        const targetElement = document.getElementById(elementId);
        if (!targetElement) return; // 要素が存在しない場合のガード

        targetElement.innerHTML = `<h3>${title}勝率:</h3>`; 

        if (Object.keys(categoryStats).length === 0) {
            targetElement.innerHTML += '<p>まだデータがないよ。</p>';
            return; 
        }

        const table = document.createElement('table');
        table.classList.add('analysis-table'); 

        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        if (categoryKey === 'opponentName') {
            headerRow.innerHTML = `
                <th>対戦相手</th>
                <th>勝利数</th>
                <th>敗北数</th>
                <th>勝率</th>
                <th>総試合数</th>
            `;
        } else {
            headerRow.innerHTML = `
                <th>${title.replace('別', '')}</th>
                <th>勝利数</th>
                <th>敗北数</th>
                <th>勝率</th>
                <th>総試合数</th>
            `;
        }
        thead.appendChild(headerRow);
        table.appendChild(thead);

        const tbody = document.createElement('tbody');

        const sortedCategories = Object.keys(categoryStats).sort((a, b) => {
            const statsA = categoryStats[a];
            const statsB = categoryStats[b];

            const winRateA = statsA.total > 0 ? (statsA.wins / statsA.total) : 0;
            const winRateB = statsB.total > 0 ? (statsB.wins / statsB.total) : 0;

            if (winRateA !== winRateB) {
                return winRateB - winRateA;
            }

            let primarySortOrder = [];
            if (categoryKey === 'myMachine' || categoryKey === 'opponentMachine') {
                primarySortOrder = MACHINES;
            } else if (categoryKey === 'stage') {
                primarySortOrder = STAGES;
            } else if (categoryKey === 'opponentName') { 
                return a.localeCompare(b); 
            }
            
            const indexA = primarySortOrder.indexOf(a);
            const indexB = primarySortOrder.indexOf(b);

            if (indexA === -1 || indexB === -1) {
                return a.localeCompare(b);
            }
            
            return indexA - indexB;
        });

        sortedCategories.forEach(category => {
            const stats = categoryStats[category];
            const wins = stats.wins;
            const losses = stats.loses; 
            const draws = stats.draws; 
            const total = stats.total;
            const winRate = total > 0 ? (wins / total * 100).toFixed(2) : 0;

            const row = document.createElement('tr');
            if (categoryKey === 'opponentName') {
                row.innerHTML = `
                    <td>${category}</td>
                    <td>${wins}</td>
                    <td>${losses}</td>
                    <td>${winRate}%</td>
                    <td>${total}</td>
                `;
            } else {
                row.innerHTML = `
                    <td>${category}</td>
                    <td>${wins}</td>
                    <td>${losses}</td>
                    <td>${winRate}%</td>
                    <td>${total}</td>
                `;
            }
            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        targetElement.appendChild(table); 
    }

    // ラウンド分析の関数
    function displayRoundsAnalysis(matches, elementId, title) {
        const overallRoundCounts = {}; 
        const winRoundCounts = {}; 
        const loseRoundCounts = {}; 
        const drawRoundCounts = {}; 

        let totalRecordedRounds = 0; 
        let totalWinsWithValidRounds = 0; 
        let totalLossesWithValidRounds = 0; 
        let totalDrawsWithValidRounds = 0; 

        matches.forEach(match => {
            if (match.rounds) { 
                const roundString = match.rounds; 
                
                totalRecordedRounds++;
                overallRoundCounts[roundString] = (overallRoundCounts[roundString] || 0) + 1;

                if (match.result === 'WIN') {
                    if (roundString === '2 vs 0' || roundString === '2 vs 1') { 
                        totalWinsWithValidRounds++;
                        winRoundCounts[roundString] = (winRoundCounts[roundString] || 0) + 1;
                    }
                } else if (match.result === 'LOSE') {
                    if (roundString === '1 vs 2' || roundString === '0 vs 2') { 
                        totalLossesWithValidRounds++;
                        loseRoundCounts[roundString] = (loseRoundCounts[roundString] || 0) + 1;
                    }
                } else if (match.result === 'DRAW') {
                    if (roundString === 'DRAW') { 
                        totalDrawsWithValidRounds++;
                        drawRoundCounts[roundString] = (drawRoundCounts[roundString] || 0) + 1;
                    }
                }
            }
        });

        const targetElement = document.getElementById(elementId);
        if (!targetElement) return; // 要素が存在しない場合のガード

        let html = `<h3>${title}割合:</h3>`;

        if (totalRecordedRounds === 0) { 
            html += '<p>まだラウンドのデータがないよ。</p>';
        } else {
            // 各ラウンドの割合（全てのラウンド記録試合に対する割合）
            html += `<h4>各ラウンドの割合:</h4>`;
            html += `<table class="analysis-table">`;
            html += `<thead><tr><th>ラウンド</th><th>試合数</th><th>割合</th></tr></thead>`;
            html += `<tbody>`;

            ROUND_OPTIONS.filter(opt => opt !== '').forEach(round => {
                const count = overallRoundCounts[round] || 0;
                const percentage = totalRecordedRounds > 0 ? (count / totalRecordedRounds * 100).toFixed(2) : 0;
                html += `<tr><td>${round}</td><td>${count}</td><td>${percentage}%</td></tr>`;
            });
            html += `</tbody></table>`;

            // WIN時のラウンド割合（WIN試合内での割合）
            html += `<h4>WIN時のラウンド割合:</h4>`; 
            if (totalWinsWithValidRounds > 0) {
                html += `<table class="analysis-table">`;
                html += `<thead><tr><th>ラウンド</th><th>勝利数</th><th>割合</th></tr></thead>`;
                html += `<tbody>`;
                ['2 vs 0', '2 vs 1'].forEach(round => { 
                    const count = winRoundCounts[round] || 0;
                    const rate = (count / totalWinsWithValidRounds * 100).toFixed(2);
                    html += `<tr><td>${round}</td><td>${count}</td><td>${rate}%</td></tr>`;
                });
                html += `</tbody></table>`;
            } else {
                html += `<p>WIN時のラウンドデータがないよ。</p>`;
            }

            // LOSE時のラウンド割合（LOSE試合内での割合）
            html += `<h4>LOSE時のラウンド割合:</h4>`; 
            if (totalLossesWithValidRounds > 0) {
                html += `<table class="analysis-table">`;
                html += `<thead><tr><th>ラウンド</th><th>敗北数</th><th>割合</th></tr></thead>`;
                html += `<tbody>`;
                ['1 vs 2', '0 vs 2'].forEach(round => { 
                    const count = loseRoundCounts[round] || 0;
                    const rate = (count / totalLossesWithValidRounds * 100).toFixed(2);
                    html += `<tr><td>${round}</td><td>${count}</td><td>${rate}%</td></tr>`;
                });
                html += `</tbody></table>`;
            } else {
                html += `<p>LOSE時のラウンドデータがないよ。</p>`;
            }
        }
        targetElement.innerHTML = html;
    }

    // 使用機体毎の対戦相手機体別勝率の関数
    function displayTwoCategoryWinRate(matches, primaryCategoryKey, secondaryCategoryKey, elementId, title) {
        const stats = {}; 

        matches.forEach(match => {
            const primary = match[primaryCategoryKey];
            const secondary = match[secondaryCategoryKey];

            if (!primary || !secondary) return; 

            if (!stats[primary]) {
                stats[primary] = {};
            }
            if (!stats[primary][secondary]) {
                stats[primary][secondary] = { wins: 0, total: 0 };
            }
            stats[primary][secondary].total++;
            if (match.result === 'WIN') {
                stats[primary][secondary].wins++;
            }
        });

        const targetElement = document.getElementById(elementId);
        if (!targetElement) return; // 要素が存在しない場合のガード

        let html = `<h3>${title}勝率:</h3>`;

        if (Object.keys(stats).length === 0) {
            html += '<p>まだデータがないよ。</p>';
        } else {
            const sortedPrimaryCategories = Object.keys(stats).sort((a, b) => {
                const indexA = MACHINES.indexOf(a);
                const indexB = MACHINES.indexOf(b);
                if (indexA === -1 || indexB === -1) return a.localeCompare(b);
                return indexA - indexB;
            });

            sortedPrimaryCategories.forEach(primaryCategory => {
                html += `<h4>${primaryCategory}:</h4>`;
                html += `<table class="analysis-table">`;
                html += `<thead><tr><th>${title.replace('別', '').split('×')[1]}</th><th>勝利数</th><th>敗北数</th><th>勝率</th><th>総試合数</th></tr></thead>`;
                html += `<tbody>`;

                const sortedSecondaryCategories = Object.keys(stats[primaryCategory]).sort((a, b) => {
                    const indexA = MACHINES.indexOf(a);
                    const indexB = MACHINES.indexOf(b);
                    if (indexA === -1 || indexB === -1) return a.localeCompare(b);
                    return indexA - indexB;
                });

                sortedSecondaryCategories.forEach(secondaryCategory => {
                    const categoryStats = stats[primaryCategory][secondaryCategory];
                    const wins = categoryStats.wins;
                    const losses = categoryStats.total - categoryStats.wins;
                    const total = categoryStats.total;
                    const winRate = total > 0 ? (wins / total * 100).toFixed(2) : 0;

                    html += `<tr>`;
                    html += `<td>${secondaryCategory}</td>`;
                    html += `<td>${wins}</td>`;
                    html += `<td>${losses}</td>`;
                    html += `<td>${winRate}%</td>`;
                    html += `<td>${total}</td>`;
                    html += `</tr>`;
                });
                html += `</tbody>`;
                html += `</table>`;
            });
        }
        targetElement.innerHTML = html;
    }

    /**
     * 使用機体ごとのステージ別勝率を表示する関数
     * @param {Array<Object>} matches - 全ての対戦記録データ
     */
    function displayMyMachineStageWinRate(matches) {
        const analysisElement = document.getElementById('my-machine-stage-winrate');
        if (!analysisElement) return; // 要素が存在しない場合のガード

        let html = '<h3>使用機体ごとのステージ別勝率:</h3>';

        const myMachineStats = {};
        matches.forEach(match => {
            const myMachine = match.myMachine;
            const stage = match.stage;
            const result = match.result;

            if (!myMachine || !stage) return; 

            if (!myMachineStats[myMachine]) {
                myMachineStats[myMachine] = {}; 
            }

            if (!myMachineStats[myMachine][stage]) {
                myMachineStats[myMachine][stage] = { wins: 0, total: 0 };
            }

            myMachineStats[myMachine][stage].total++;
            if (result === 'WIN') {
                myMachineStats[myMachine][stage].wins++;
            }
        });

        if (Object.keys(myMachineStats).length === 0) {
            html += '<p>まだデータがないよ。</p>';
        } else {
            MACHINES.forEach(machine => {
                if (myMachineStats[machine]) { 
                    html += `<h4>${machine}:</h4>`; 

                    html += `<table class="analysis-table">`;
                    html += `<thead><tr><th>ステージ</th><th>勝利数</th><th>敗北数</th><th>勝率</th><th>総試合数</th></tr></thead>`;
                    html += `<tbody>`;

                    STAGES.forEach(stage => {
                        if (myMachineStats[machine][stage]) { 
                            const stats = myMachineStats[machine][stage];
                            const wins = stats.wins;
                            const losses = stats.total - stats.wins;
                            const total = stats.total;
                            const winRate = total > 0 ? (wins / total * 100).toFixed(2) : 0;

                            html += `<tr>`;
                            html += `<td>${stage}</td>`;
                            html += `<td>${wins}</td>`;
                            html += `<td>${losses}</td>`;
                            html += `<td>${winRate}%</td>`;
                            html += `<td>${total}</td>`;
                            html += `</tr>`;
                        }
                    });
                    html += `</tbody>`;
                    html += `</table>`;
                }
            });
        }
        analysisElement.innerHTML = html;
    }

    // CSV出力ボタンのイベントリスナー
    if (exportCsvButton) { // 要素が存在するかチェック
        exportCsvButton.addEventListener('click', async () => {
            const success = await window.api.exportCsv();
            if (success) {
                alert('CSVファイルを保存したよ！');
            } else {
                alert('CSVファイルの保存をキャンセルしたよ、または失敗したよ。');
            }
        });
    }

    // deleteAllRecordsButton のイベントリスナー
    if (deleteAllRecordsButton) { // 要素が存在するかチェック
        deleteAllRecordsButton.addEventListener('click', async () => {
            const response1 = await window.api.confirmFirstDelete(); 
            if (!response1) {
                return;
            }

            const response2 = await window.api.confirmFinalDelete(); 
            if (!response2) {
                return;
            }

            const success = await window.api.deleteAllRecords();
            if (success) {
                alert('すべての対戦記録を削除したよ！');
                await updateAnalysis();
                await displayMatches();
            } else {
                alert('対戦記録の削除に失敗したよ。');
            }
        });
    }

    // initializeUI関数はDOMContentLoaded内で呼び出されるため、ここには不要
    // initializeUI();
});
