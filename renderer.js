// renderer.js
import { MACHINES, STAGES, RECENT_MATCH_COUNT, MARK_ITEM_TOTAL_WIDTH, MACHINE_KEY_MAP, STAGE_KEY_MAP, MACHINE_COLOR_MAP, RESULT_ROUND_OPTIONS, RESULT_ROUND_KEY_MAP, RESULT_ROUND_COLOR_MAP, RESULT_ROUND_SYMBOL_MAP } from './constants.js'
import { populateSelect, normalizeResult } from './utils.js'
import { updateAnalysis } from './analysis.js' // updateAnalysisをimport

// 接続と対戦記録の連携に関するUI要素
let myMachineSelect, opponentMachineSelect, stageSelect, resultSelect, opponentNameInput, opponentNameSuggestionsContainer, addRecordButton, exportCsvButton, deleteAllRecordsButton, myMachineColorIndicator, opponentMachineColorIndicator, resultColorIndicator
let matchesTableBody, noRecordsMessage
let recentWinLossCountElement, recentWinLossMarksElement, resetRecentMatchesButton, messageSpan, deleteIdInput, deleteByIdButton

// 分析結果表示用のUI要素
let overallWinrateElement, myMachineWinrateElement, opponentMachineWinrateElement, stageWinrateElement
let roundsAnalysisElement, myMachineOpponentAnalysisElement, opponentNameWinrateElement, myMachineStageWinrateElement

let allMatches = [] // 全ての対戦記録を保持する変数
let allOpponentNames = [] // ★追加: 全対戦相手名をキャッシュする配列
let currentOpponentSuggestions = [] // 現在表示中のサジェスト
let highlightedSuggestionIndex = -1 // ハイライトされているサジェストのインデックス
let shouldConfirmRegister = true

// ★追加: サウンド関連
let soundEnabled = true
const moveSound = new Audio('./assets/sounds/move-snd.mp3')
const regSound = new Audio('./assets/sounds/reg-snd.mp3')
const tabSound = new Audio('./assets/sounds/tab-snd.mp3') // ★追加

/**
 * 画面上部のメッセージ表示を更新する。
 * @param {string} text - 表示するメッセージテキスト。
 * @param {string} [type='default'] - メッセージの種類 (green, blue, sienna, red)。
 */
function updateMessage (text, type = 'default') {
  if (messageSpan) {
    messageSpan.textContent = text
    // CSSでスタイリングを制御するためにデータ属性を設定
    messageSpan.dataset.type = type
  }
}

/**
 * ★追加: サウンドを再生する。
 * @param {HTMLAudioElement} audioElement - 再生するAudioオブジェクト。
 */
function playSound (audioElement) {
  if (!soundEnabled) return
  // 再生中の場合は、一度停止して最初から再生し直す（音が重なるのを防ぐ）
  if (!audioElement.paused) {
    audioElement.pause()
    audioElement.currentTime = 0
  }
  // play()はPromiseを返す。ユーザーがページを操作する前に再生しようとすると失敗することがあるため、catchでエラーを握りつぶす
  const playPromise = audioElement.play()
  if (playPromise !== undefined) {
    playPromise.catch(error => console.warn('サウンドの再生に失敗しました:', error))
  }
}

// --- DOMContentLoadedイベントリスナー ---
document.addEventListener('DOMContentLoaded', () => {
  myMachineSelect = document.getElementById('myMachine')
  opponentMachineSelect = document.getElementById('opponentMachine')
  stageSelect = document.getElementById('stage')
  resultSelect = document.getElementById('result')
  opponentNameInput = document.getElementById('opponentName')
  opponentNameSuggestionsContainer = document.getElementById('opponentNameSuggestions') // ★追加
  myMachineColorIndicator = document.getElementById('myMachineColor')
  opponentMachineColorIndicator = document.getElementById('opponentMachineColor')
  resultColorIndicator = document.getElementById('resultColor')
  addRecordButton = document.getElementById('addRecordButton')
  exportCsvButton = document.getElementById('exportCsvButton')
  deleteAllRecordsButton = document.getElementById('deleteAllRecordsButton')
  matchesTableBody = document.querySelector('#matches-table tbody')
  noRecordsMessage = document.getElementById('no-records-message')
  recentWinLossCountElement = document.getElementById('recentWinLossCount')
  recentWinLossMarksElement = document.getElementById('recentWinLossMarks')
  resetRecentMatchesButton = document.getElementById('resetRecentMatchesButton')
  deleteIdInput = document.getElementById('deleteIdInput')
  deleteByIdButton = document.getElementById('deleteByIdButton')
  messageSpan = document.getElementById('message')

  // 分析結果表示用のUI要素を取得
  overallWinrateElement = document.getElementById('overall-winrate')
  myMachineWinrateElement = document.getElementById('my-machine-winrate')
  opponentMachineWinrateElement = document.getElementById('opponent-machine-winrate')
  stageWinrateElement = document.getElementById('stage-winrate')
  roundsAnalysisElement = document.getElementById('rounds-analysis')
  myMachineOpponentAnalysisElement = document.getElementById('my-machine-opponent-analysis')
  opponentNameWinrateElement = document.getElementById('opponent-name-winrate')
  myMachineStageWinrateElement = document.getElementById('my-machine-stage-winrate')

  // ★追加: 登録確認ダイアログ表示設定のロードとイベントリスナー
  const savedConfirmRegisterSetting = localStorage.getItem('confirmRegister')
  shouldConfirmRegister = savedConfirmRegisterSetting !== null ? savedConfirmRegisterSetting === 'true' : true // デフォルトは確認する

  const confirmRegisterCheckbox = document.getElementById('confirmRegisterCheckbox')
  if (confirmRegisterCheckbox) {
    confirmRegisterCheckbox.checked = shouldConfirmRegister // 初期状態を設定
    confirmRegisterCheckbox.addEventListener('change', () => {
      shouldConfirmRegister = confirmRegisterCheckbox.checked
      localStorage.setItem('confirmRegister', shouldConfirmRegister.toString())
      updateMessage(`登録前の確認ダイアログを【${shouldConfirmRegister ? '表示する' : '表示しない'}】ように設定したよ！`, 'blue')
    })
  }
  // ★追加: サウンド設定のロードとイベントリスナー
  const savedSoundSetting = localStorage.getItem('soundEnabled')
  soundEnabled = savedSoundSetting !== null ? savedSoundSetting === 'true' : false // ★修正: デフォルトはOFF

  const soundEnabledCheckbox = document.getElementById('soundEnabledCheckbox')
  if (soundEnabledCheckbox) {
    soundEnabledCheckbox.checked = soundEnabled
    soundEnabledCheckbox.addEventListener('change', () => {
      soundEnabled = soundEnabledCheckbox.checked
      localStorage.setItem('soundEnabled', soundEnabled.toString())
      updateMessage(`サウンドを【${soundEnabled ? '再生する' : '再生しない'}】ように設定したよ！`, 'blue')
      playSound(moveSound) // 設定変更時に確認音を鳴らす
    })
  }

  /**
   * select要素で選択された値に応じて、色のインジケーターを更新する。
   * @param {HTMLSelectElement} selectElement - select要素。
   * @param {HTMLElement} colorIndicator - 色を表示するspan要素。
   * @param {object} colorMap - 値と色の対応マップ。
   */
  function updateColorIndicator (selectElement, colorIndicator, colorMap) {
    if (!selectElement || !colorIndicator) return

    const selectedValue = selectElement.value
    const color = colorMap[selectedValue] || 'transparent' // マップにない場合は透明に

    colorIndicator.style.backgroundColor = color
  }

  // 機体選択セレクトボックスの変更イベントリスナー
  if (myMachineSelect && myMachineColorIndicator) {
    myMachineSelect.addEventListener('change', () => {
      updateColorIndicator(myMachineSelect, myMachineColorIndicator, MACHINE_COLOR_MAP)
      playSound(moveSound)
    })
  }
  if (opponentMachineSelect && opponentMachineColorIndicator) {
    opponentMachineSelect.addEventListener('change', () => {
      updateColorIndicator(opponentMachineSelect, opponentMachineColorIndicator, MACHINE_COLOR_MAP)
      playSound(moveSound)
    })
  }
  // ★追加: ステージとラウンドのセレクトボックスにもサウンドを追加
  if (stageSelect) {
    stageSelect.addEventListener('change', () => playSound(moveSound))
  }

  // ★追加: プレースホルダーのスタイルを管理するロジック
  const selectElementsWithPlaceholder = [resultSelect]

  /**
   * select要素の値が空かどうかで、プレースホルダークラスを付け外しする。
   * @param {HTMLSelectElement} selectElement - 対象のselect要素。
   */
  function updatePlaceholderClass (selectElement) {
    if (selectElement) {
      if (selectElement.value === '') {
        selectElement.classList.add('is-placeholder')
      } else {
        selectElement.classList.remove('is-placeholder')
      }
    }
  }

  selectElementsWithPlaceholder.forEach(selectElement => {
    if (selectElement) {
      // 各セレクトボックスに、一度だけイベントリスナーを登録
      selectElement.addEventListener('change', () => updatePlaceholderClass(selectElement))
    }
  })

  // ★修正: 勝敗/ラウンドのイベントリスナー
  if (resultSelect) {
    resultSelect.addEventListener('change', (event) => {
      if (event.isTrusted) {
        playSound(moveSound)
      }

      const selectedValue = resultSelect.value

      // ★修正: 勝敗インジケーターに、ラウンドごとの色とシンボルを設定
      if (resultColorIndicator) {
        const color = RESULT_ROUND_COLOR_MAP[selectedValue] || 'transparent'
        const symbol = RESULT_ROUND_SYMBOL_MAP[selectedValue] || ''
        resultColorIndicator.style.backgroundColor = color
        resultColorIndicator.textContent = symbol

        // ★追加: ストレート勝ち(◎)の場合、特別なクラスを付与してスタイルを調整
        if (selectedValue === 'WIN_2v0') {
          resultColorIndicator.classList.add('is-straight-win')
        } else {
          resultColorIndicator.classList.remove('is-straight-win')
        }
      }
    })
  }

  // ★追加: 対戦相手名入力のサジェスト機能
  if (opponentNameInput) {
    opponentNameInput.addEventListener('input', showOpponentSuggestions)
    // フォーカスが外れた時にサジェストを隠す（少し遅延させる）
    opponentNameInput.addEventListener('blur', () => {
      // 150msの遅延は、サジェスト項目をクリックする時間を確保するため
      setTimeout(hideOpponentSuggestions, 150)
    })
  }

  /**
     * アプリケーション起動時にUIを初期化する。セレクトボックスの生成、前回入力値のロード、分析データの表示を行う。
     */
  async function initializeUI () {
    populateSelect('myMachine', MACHINES, MACHINE_KEY_MAP)
    populateSelect('opponentMachine', MACHINES, MACHINE_KEY_MAP)
    populateSelect('stage', STAGES, STAGE_KEY_MAP)
    populateSelect('result', RESULT_ROUND_OPTIONS, RESULT_ROUND_KEY_MAP)

    // 前回入力した内容をロード
    loadLastInput()

    // 初期表示時の色を更新
    updateColorIndicator(myMachineSelect, myMachineColorIndicator, MACHINE_COLOR_MAP)

    // ★修正: loadLastInput()で設定された後、プレースホルダークラスを更新
    selectElementsWithPlaceholder.forEach(updatePlaceholderClass)

    allMatches = (await window.api.getMatches()) || [] // データを最初に取得
    updateAnalysis(allMatches, overallWinrateElement, myMachineWinrateElement, opponentMachineWinrateElement, stageWinrateElement, roundsAnalysisElement, myMachineOpponentAnalysisElement, opponentNameWinrateElement, myMachineStageWinrateElement)
    await updateOpponentNames()
    await displayMatches(allMatches) // 取得したデータを渡す

    // ★修正: 初期表示時に勝敗セレクトボックスのchangeイベントを発火させて、ラウンドとインジケーターを更新
    if (resultSelect) {
      resultSelect.dispatchEvent(new Event('change'))
    }
  }

  /**
     * localStorageから前回入力したフォームの内容を読み込み、フォームに設定する。
     */
  function loadLastInput () {
    const lastMyMachine = localStorage.getItem('lastMyMachine')
    const lastOpponentMachine = localStorage.getItem('lastOpponentMachine')
    const lastStage = localStorage.getItem('lastStage')
    const lastResultRound = localStorage.getItem('lastResultRound')
    // ★修正: 対戦相手名もlocalStorageからロード
    const lastOpponentName = localStorage.getItem('lastOpponentName')

    if (myMachineSelect && lastMyMachine) myMachineSelect.value = lastMyMachine
    if (opponentMachineSelect && lastOpponentMachine) opponentMachineSelect.value = lastOpponentMachine
    if (stageSelect && lastStage) stageSelect.value = lastStage
    if (resultSelect && lastResultRound) resultSelect.value = lastResultRound
    // ★修正: ロードした対戦相手名をフォームに反映 (nullish coalescing operatorで空文字列にフォールバック)
    if (opponentNameInput && lastOpponentName !== null) opponentNameInput.value = lastOpponentName ?? ''
  }

  /**
     * DBから対戦相手名のリストを再取得し、入力フォームのサジェスト(<datalist>)を更新する。
     */
  async function updateOpponentNames () {
    const matches = (await window.api.getMatches()) || []
    const uniqueNames = [...new Set(matches.map(m => m.opponentName).filter(Boolean))]
    allOpponentNames = uniqueNames // キャッシュを更新
  }

  /**
 * 入力内容に基づいて対戦相手名のサジェストを表示する。
 */
  function showOpponentSuggestions () {
    const inputValue = opponentNameInput.value.trim().toLowerCase()

    hideOpponentSuggestions() // 既存のサジェストをクリア

    if (inputValue.length === 0) {
      return // 入力がなければ何もしない
    }

    // キャッシュされたリストからフィルタリング
    const filteredNames = allOpponentNames.filter(name => name.toLowerCase().includes(inputValue))

    const suggestions = filteredNames.slice(0, 5) // 上位5件に絞る
    currentOpponentSuggestions = suggestions // 現在のサジェストを保持

    if (suggestions.length > 0) {
      const fragment = document.createDocumentFragment()
      suggestions.forEach((name, index) => {
        const suggestionDiv = document.createElement('div')
        suggestionDiv.className = 'suggestion-item'
        suggestionDiv.textContent = name // [Alt+*] の表示を削除

        // 'click'の代わりに'mousedown'を使い、'blur'イベントより先に発火させる
        suggestionDiv.addEventListener('mousedown', () => {
          opponentNameInput.value = name
          hideOpponentSuggestions()
        })
        fragment.appendChild(suggestionDiv)
      })
      opponentNameSuggestionsContainer.appendChild(fragment)
      opponentNameSuggestionsContainer.style.display = 'block'
    }
  }

  /**
 * 対戦相手名のサジェストを非表示にする。
 */
  function hideOpponentSuggestions () {
    if (opponentNameSuggestionsContainer) {
      opponentNameSuggestionsContainer.innerHTML = ''
      opponentNameSuggestionsContainer.style.display = 'none'
    }
    currentOpponentSuggestions = []
    highlightedSuggestionIndex = -1 // ハイライトをリセット
  }

  /**
 * サジェストリストのハイライト表示を更新する。
 */
  function updateSuggestionHighlight () {
    if (!opponentNameSuggestionsContainer) return
    const items = opponentNameSuggestionsContainer.querySelectorAll('.suggestion-item')
    items.forEach((item, index) => {
      if (index === highlightedSuggestionIndex) {
        item.classList.add('highlighted')
        // スクロールして表示範囲内に持ってくる
        item.scrollIntoView({ block: 'nearest', inline: 'start' })
      } else {
        item.classList.remove('highlighted')
      }
    })
  }

  // リセットボタンのイベントリスナー (変更なし)
  if (resetRecentMatchesButton) {
    resetRecentMatchesButton.addEventListener('click', () => {
      localStorage.setItem('recentMatchesResetTimestamp', Date.now().toString())
      displayMatches(allMatches) // ★修正: allMatchesを渡す
    })
  }

  /**
     * 直近25戦の勝敗数と勝敗マーク（〇✕△）を表示する。
     * @param {number} wins - 勝利数。
     * @param {number} losses - 敗北数。
     * @param {Array<string>} marksArray - 勝敗マークのHTML文字列の配列。
     */
  function displayRecentMatchesSummary (wins, losses, marksArray) {
    if (!recentWinLossCountElement || !recentWinLossMarksElement) return

    recentWinLossCountElement.innerHTML = `自分: ${wins}&nbsp;&nbsp;vs&nbsp;&nbsp;相手: ${losses}`
    recentWinLossMarksElement.innerHTML = marksArray.join('')

    const recentMatchesSummaryDiv = document.querySelector('.recent-matches-summary')

    const resetTimestamp = localStorage.getItem('recentMatchesResetTimestamp')
    const isInitialEmptyState = (wins === 0 && losses === 0 && marksArray.every(mark => mark.includes('none')) && !resetTimestamp)

    if (recentMatchesSummaryDiv) {
      if (isInitialEmptyState) {
        recentMatchesSummaryDiv.style.display = 'none'
      } else {
        recentMatchesSummaryDiv.style.display = 'block'
      }
    }

    const recentMarksGuideElement = recentMatchesSummaryDiv ? recentMatchesSummaryDiv.querySelector('.recent-marks-guide') : null
    if (recentMarksGuideElement) {
      recentMarksGuideElement.innerHTML = ''

      const GUIDE_NUMBERS = [5, 10, 15, 20, 25, 30, 35]

      GUIDE_NUMBERS.forEach(num => {
        if (num <= RECENT_MATCH_COUNT) {
          const guideSpan = document.createElement('span')
          guideSpan.textContent = num.toString()

          guideSpan.style.left = `${(num / 5) * 115 + ((num / 5) + 2)}px`

          recentMarksGuideElement.appendChild(guideSpan)
        }
      })

      recentMarksGuideElement.style.width = `${RECENT_MATCH_COUNT * MARK_ITEM_TOTAL_WIDTH}px`
    }
    if (recentWinLossMarksElement) {
      recentWinLossMarksElement.style.width = `${RECENT_MATCH_COUNT * MARK_ITEM_TOTAL_WIDTH}px`
    }
  }

  /**
     * DBから全対戦記録を取得し、直近25戦のサマリーと、最新100件の対戦記録一覧テーブルを画面に表示する。
     * 記録がない場合はその旨のメッセージを表示する。
     */
  async function displayMatches (matches) { // 引数でmatchesを受け取るように変更
    // const allMatches = (await window.api.getMatches()) || [] // initializeUIで取得済みなので不要

    if (!matchesTableBody || !noRecordsMessage) return
    // ★修正: displayMatches関数が引数で受け取ったmatchesを使うように修正
    matchesTableBody.innerHTML = ''

    if (allMatches.length === 0) {
      localStorage.removeItem('recentMatchesResetTimestamp')
      displayRecentMatchesSummary(0, 0, Array(RECENT_MATCH_COUNT).fill('<div class="recent-mark none">-</div>'))
      const summaryDiv = document.querySelector('.recent-matches-summary')
      if (summaryDiv) summaryDiv.style.display = 'none'

      noRecordsMessage.style.display = 'block'
      matchesTableBody.style.display = 'none'
    } else {
      noRecordsMessage.style.display = 'none' // 記録がある場合は非表示
      matchesTableBody.style.display = 'table-row-group'

      const resetTimestamp = localStorage.getItem('recentMatchesResetTimestamp')
      const resetTime = resetTimestamp ? parseInt(resetTimestamp, 10) : 0

      let wins = 0
      let losses = 0

      const marks = Array(RECENT_MATCH_COUNT).fill('<div class="recent-mark none">-</div>')

      // ★修正: allMatchesではなく、引数で受け取ったmatchesを使う
      const relevantMatches = matches.filter(match => parseInt(match.id, 10) > resetTime)
      const matchesToDisplay = relevantMatches.slice(0, RECENT_MATCH_COUNT)
      const orderedMatchesForDisplay = [...matchesToDisplay].reverse()

      orderedMatchesForDisplay.forEach((match, index) => {
        const normalizedResult = normalizeResult(match.result)

        if (normalizedResult === 'WIN') {
          marks[index] = '<div class="recent-mark win">〇</div>'
          wins++
        } else if (normalizedResult === 'LOSE') {
          marks[index] = '<div class="recent-mark lose">✕</div>'
          losses++
        } else if (normalizedResult === 'DRAW') {
          marks[index] = '<div class="recent-mark draw">△</div>'
        }
      })

      displayRecentMatchesSummary(wins, losses, marks) // allMatchesではなく、引数で受け取ったmatchesを使う

      const latestMatches = matches.slice(0, 100)
      // latestMatches.forEach ループ内で直接DOM操作を行う

      latestMatches.forEach(match => {
        const row = document.createElement('tr')

        const date = match.id ? new Date(match.id) : new Date()
        const formattedDate = date.toLocaleString('ja-JP', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }).replace(/\//g, '/').replace(',', '')

        const displayResult = normalizeResult(match.result)
        let resultClass = ''
        if (displayResult === 'WIN') {
          resultClass = 'result-win'
        } else if (displayResult === 'LOSE') {
          resultClass = 'result-lose'
        } else if (displayResult === 'DRAW') {
          resultClass = 'result-draw'
        } else {
          resultClass = 'result-other'
        }

        // 各セルを生成して行に追加
        const cells = [
          formattedDate,
          match.myMachine,
          match.opponentMachine,
          match.stage,
          // 勝敗セルは<span>で囲んでクラスを適用
          `<span class="${resultClass}">${displayResult}</span>`,
          match.rounds || '-',
          match.opponentName || '-',
          match.id
        ]

        cells.forEach((content, index) => {
          const td = document.createElement('td')
          if (index === 4) { // 勝敗のセル
            td.innerHTML = content // HTML文字列を直接設定
          } else {
            td.textContent = content // その他のセルはテキストとして設定
          }
          row.appendChild(td)
        })
        matchesTableBody.appendChild(row)
      })
    }
  }

  // アプリのロード時に一度だけ initializeUI を呼び出す
  initializeUI()

  // ★追加: 記録追加処理を関数として抽出
  async function addRecord () {
    const myMachine = myMachineSelect.value
    const opponentMachine = opponentMachineSelect.value
    const stage = stageSelect.value
    const resultRoundValue = resultSelect.value
    const opponentName = opponentNameInput.value.trim()

    if (!myMachine || !opponentMachine || !stage || !resultRoundValue) {
      updateMessage('必須項目をすべて入力してね！', 'sienna')
      return
    }

    // ★修正: 選択された値から勝敗とラウンドを分割
    const [result, roundParts] = resultRoundValue.split('_') // 'WIN_2v0' -> ['WIN', '2v0']
    const rounds = roundParts ? roundParts.replace('v', ' vs ') : null // '2v0' -> '2 vs 0'

    // ★修正: 選択肢のテキストを取得して確認ダイアログに表示
    const resultRoundText = resultSelect.options[resultSelect.selectedIndex].text

    // ★修正: 確認ダイアログのロジックをここに集約
    if (shouldConfirmRegister) {
      const confirmationMessage = [
        `使用機体: ${myMachine}`,
        `相手機体: ${opponentMachine}`,
        `ステージ: ${stage}`,
        `勝敗/ラウンド: ${resultRoundText}`,
        `相手名: ${opponentName || '(未入力)'}`
      ].join('\n') // 改行で連結

      const isConfirmed = await window.api.showConfirmDialog({
        title: '登録内容の確認',
        message: 'この内容で記録する？',
        detail: confirmationMessage,
        buttons: ['はい、登録します', 'いいえ、やめます']
      })
      if (!isConfirmed) {
        updateMessage('記録の追加、やめておいたよ。', 'sienna')
        myMachineSelect.focus() // キャンセル後も自分の機体選択にフォーカス
        return // 処理を中断
      }
    }

    const newMatch = {
      id: Date.now(),
      myMachine,
      opponentMachine,
      stage,
      result, // 'WIN' or 'LOSE'
      rounds: rounds || null,
      opponentName: opponentName || null
    }

    try {
      await window.api.addMatch(newMatch) // 記録を追加
      playSound(regSound)
      updateMessage('記録を追加したよ！', 'green')

      localStorage.setItem('lastMyMachine', myMachine)
      localStorage.setItem('lastOpponentMachine', opponentMachineSelect.value)
      localStorage.setItem('lastStage', stage)
      localStorage.setItem('lastOpponentName', opponentName)

      // 登録後、勝敗/ラウンドを未選択状態にリセット
      localStorage.setItem('lastResultRound', '')

      resultSelect.value = ''

      await initializeUI() // UIを再初期化して全データを更新

      opponentNameInput.value = '' // フォームの対戦相手名フィールドはクリア

      // ★修正: 登録後、自分の機体選択にフォーカスを戻す
      setTimeout(async () => {
        await window.api.forceReactivateWindow()
        myMachineSelect.focus()
      }, 100)
    } catch (error) {
      updateMessage(`ごめんね、記録の追加に失敗しちゃった…: ${error.message}`, 'red')
      console.error('記録追加エラー:', error)
    }
  }

  // 記録追加ボタンのイベントリスナー
  if (addRecordButton) {
    addRecordButton.addEventListener('click', addRecord) // ★修正: 抽出した関数を呼び出す
  }

  // ★追加: キーボードショートカットのイベントリスナー
  document.addEventListener('keydown', (event) => {
    const activeElement = document.activeElement
    const key = event.key ? event.key.toLowerCase() : '' // ★修正: event.keyが存在するかチェック
    const isInputSelected = activeElement === opponentNameInput

    // ★追加: Tabキーでのフォーカス移動時にサウンドを再生
    // このロジックは、後続の処理でTabキーが補足されてreturnされる場合よりも先に実行される必要がある
    if (key === 'tab') {
      playSound(tabSound)
      // ここでreturnしないことで、ブラウザのデフォルトのTab移動や、他のTabキー処理が実行される
    }

    // ★追加: Alt + X で記録を追加
    if (event.altKey && key === 'x') {
      event.preventDefault() // デフォルト動作を抑制
      addRecord() // 記録追加処理を呼び出す
      return // 他のショートカットが発動しないように
    }

    // Enterキーが押された場合の処理 (対戦相手名入力欄にフォーカスがある場合)
    // Enterキーが押された場合の処理（対戦相手名入力欄が選択されている場合）
    if (key === 'enter' && isInputSelected) {
      event.preventDefault() // デフォルトのEnterキー動作（フォーム送信、改行など）を抑制
      addRecord() // 記録追加処理を呼び出す
      return // 処理はここで終了
    }

    // ★修正: 対戦相手名サジェストのキーボードナビゲーション
    if (isInputSelected && currentOpponentSuggestions.length > 0) {
      const isUp = (event.altKey && (key === 'w' || key === 'a')) || key === 'arrowup'
      const isDown = (event.altKey && (key === 's' || key === 'd')) || key === 'arrowdown'

      if (isUp || isDown) {
        event.preventDefault()
        // ★追加: サジェスト移動時にもサウンドを再生
        if (currentOpponentSuggestions.length > 0) {
          playSound(moveSound)
        }
        const numSuggestions = currentOpponentSuggestions.length
        if (isUp) {
          highlightedSuggestionIndex = (highlightedSuggestionIndex - 1 + numSuggestions) % numSuggestions
        } else { // isDown
          highlightedSuggestionIndex = (highlightedSuggestionIndex + 1) % numSuggestions
        }
        updateSuggestionHighlight()
        return
      }
      // EnterまたはTabでサジェストを確定
      if ((key === 'enter' || key === 'tab') && highlightedSuggestionIndex > -1) {
        event.preventDefault()
        opponentNameInput.value = currentOpponentSuggestions[highlightedSuggestionIndex]
        hideOpponentSuggestions()
        // Enterでの記録追加が暴発しないようにここで処理を終了
        return
      }
    }

    // 1-4, qwerキーでのselect要素操作
    if (activeElement && activeElement.tagName === 'SELECT') {
      // ★追加: Alt + WASDでの選択肢ナビゲーション
      if (event.altKey && ['w', 'a', 's', 'd'].includes(key)) {
        event.preventDefault() // WASDのデフォルト動作（あれば）を抑制
        const options = Array.from(activeElement.options)
        const currentIndex = activeElement.selectedIndex
        let newIndex = currentIndex

        switch (key) {
          case 'w': // Up
          case 'a': // Left
            newIndex = currentIndex - 1
            break
          case 's': // Down
          case 'd': // Right
            newIndex = currentIndex + 1
            break
        }

        // 新しいインデックスが範囲内で、かつ無効なオプション（プレースホルダーなど）でないか確認
        if (newIndex >= 0 && newIndex < options.length && !options[newIndex].disabled) {
          activeElement.selectedIndex = newIndex
          activeElement.dispatchEvent(new Event('change'))
          playSound(moveSound) // ★追加: ショートカット操作時にもサウンドを再生
        }
        // Alt+WASDの処理はここで終了（下のキーマップ処理には進まない）
        return
      }

      const selectId = activeElement.id
      let currentKeyMap = null

      // どのselect要素かによって使用するキーマップを決定
      if (selectId === 'myMachine' || selectId === 'opponentMachine') {
        currentKeyMap = MACHINE_KEY_MAP
      } else if (selectId === 'stage') {
        currentKeyMap = STAGE_KEY_MAP
      } else if (selectId === 'result') {
        currentKeyMap = RESULT_ROUND_KEY_MAP
      }

      if (currentKeyMap && currentKeyMap[key]) {
        activeElement.value = currentKeyMap[key]
        // JavaScriptで値を変更した場合はchangeイベントを手動で発火させる
        activeElement.dispatchEvent(new Event('change'))
        playSound(moveSound) // ★追加: ショートカット操作時にもサウンドを再生
        event.preventDefault() // ブラウザのデフォルト動作（例: 'q'でクイック検索）を抑制
      }
    }
  }
  )

  // CSV出力ボタンのイベントリスナー
  if (exportCsvButton) {
    exportCsvButton.addEventListener('click', async () => {
      const success = await window.api.exportCsv()
      if (success) {
        updateMessage('CSVファイルを保存したよ！', 'green')
      } else {
        updateMessage('CSVファイルの保存、やめておいたよ。', 'sienna')
      }
    })
  }

  // deleteAllRecordsButton のイベントリスナー
  if (deleteAllRecordsButton) {
    deleteAllRecordsButton.addEventListener('click', async () => {
      const response1 = await window.api.confirmFirstDelete()
      if (!response1) {
        return
      }

      const response2 = await window.api.confirmFinalDelete()
      if (!response2) {
        return
      }

      const success = await window.api.deleteAllRecords()
      if (success) {
        playSound(regSound) // ★追加
        updateMessage('すべての対戦記録を削除したよ！', 'green')
        await initializeUI() // UIを再初期化して全データを更新
        // await updateAnalysis() // initializeUIで呼ばれるので不要
        // await displayMatches() // initializeUIで呼ばれるので不要
      } else {
        updateMessage('ごめんね、記録の削除に失敗しちゃった…', 'red')
      }
    })
  }

  // ★追加: 指定IDの記録を削除するボタンのイベントリスナー
  if (deleteByIdButton && deleteIdInput) {
    deleteByIdButton.addEventListener('click', async () => {
      const idString = deleteIdInput.value.trim()
      if (!idString) {
        updateMessage('削除したい記録のIDを入力してね！', 'sienna')
        deleteIdInput.focus()
        return
      }

      const idToDelete = Number(idString)
      if (isNaN(idToDelete) || !Number.isInteger(idToDelete)) {
        updateMessage('IDは半角数字で入力してね！', 'sienna')
        deleteIdInput.focus()
        return
      }

      const matchToDelete = allMatches.find(m => m.id === idToDelete)

      if (!matchToDelete) {
        updateMessage(`ID「${idToDelete}」の記録が見つからなかったよ。`, 'red')
        deleteIdInput.focus()
        return
      }

      const date = new Date(matchToDelete.id)
      const formattedDate = date.toLocaleString('ja-JP', {
        year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit'
      }).replace(/\//g, '/').replace(',', '')

      const confirmationMessage = [
        '以下の記録を削除します。よろしいですか？',
        '--------------------',
        `ID: ${matchToDelete.id}`,
        `日時: ${formattedDate}`,
        `内容: ${matchToDelete.myMachine} vs ${matchToDelete.opponentMachine} (${matchToDelete.result})`
      ].join('\n')

      const isConfirmed = await window.api.showConfirmDialog({
        title: '削除の確認',
        message: 'この記録を削除する？',
        detail: confirmationMessage,
        buttons: ['はい、削除します', 'いいえ、やめます']
      })
      if (isConfirmed) {
        const success = await window.api.deleteMatch(idToDelete)
        if (success) {
          playSound(regSound) // ★追加
          updateMessage(`ID「${idToDelete}」の記録を削除したよ！`, 'blue')
          deleteIdInput.value = '' // 入力欄をクリア
          await initializeUI() // UIを再描画
        } else {
          updateMessage('ごめんね、記録の削除に失敗しちゃった…', 'red')
        }
      } else {
        updateMessage('削除、やめておいたよ。', 'sienna')
      }
    })
  }
})
