module.exports = {
  // ESLintが親ディレクトリのeslintrcファイルを探索するのを停止
  root: true,
  // 環境設定: Node.jsとブラウザ環境のグローバル変数を有効にする
  env: {
    node: true, // Node.jsのグローバル変数（process, requireなど）を有効にする
    browser: true, // ブラウザのグローバル変数（window, documentなど）を有効にする
    es2021: true // ES2021の構文を有効にする
  },
  // 拡張設定: 既存のルールセットを継承
  extends: [
    'eslint:recommended', // ESLintの推奨ルール
    'standard' // standardスタイルガイド
  ],
  // パーサーオプション: ECMAScriptのバージョンとモジュールタイプを指定
  parserOptions: {
    ecmaVersion: 12, // ECMAScript 2021
    sourceType: 'module' // ES Modulesを使用
  },
  // プラグイン: 特定の機能やフレームワークのルールを追加
  plugins: [
    // 'node', // eslint-plugin-n に置き換えられることが多い
    // 'import', // eslint-plugin-import
    // 'promise', // eslint-plugin-promise
    'n' // eslint-plugin-n (Node.js固有のルール)
  ],
  // 個別のルール設定: ここでデフォルトのルールを上書きしたり、新しいルールを追加したりできる
  rules: {
    // 例: console.logの使用を警告にする（本番コードに残さないため）
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // 例: debuggerの使用を警告にする（本番コードに残さないため）
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
}
