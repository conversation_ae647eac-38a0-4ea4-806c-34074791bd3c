{"name": "virtualon-omg-record-app", "version": "1.1.3", "description": "", "main": "main.js", "scripts": {"clean": "rimraf release-builds dist", "start": "electron .", "test": "echo \"Error: no test specified\" && exit 1", "package": "npm run clean && electron-packager . --overwrite --platform=win32 --arch=x64 --out=release-builds --prune=true --ignore=node_modules/electron --ignore=node_modules/electron-packager --ignore=readme-begore-packaging.txt"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"chokidar": "^4.0.3", "lowdb": "^7.0.1"}, "devDependencies": {"electron": "^36.4.0", "electron-packager": "^17.1.2"}}